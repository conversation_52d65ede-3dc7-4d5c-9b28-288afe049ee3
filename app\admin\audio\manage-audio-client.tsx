"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useAnimations } from "@/hooks/use-animations";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Plus,
  Edit,
  Trash2,
  Play,
  Pause,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Volume2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { AudioComparison } from "@/lib/audio-data";
import {
  validateAudioUrl,
  AudioValidationResult,
  formatDuration,
  AudioPreview,
} from "@/lib/audio-validation";
import {
  audioComparisonManager,
  CreateComparisonData,
} from "@/lib/audio-management";

interface FormData {
  title: string;
  artist: string;
  category: "mixing-comparison" | "mastering-comparison";
  projectId: string;
  beforeUrl: string;
  afterUrl: string;
  description: string;
  tags: string;
  year: string;
}

const initialFormData: FormData = {
  title: "",
  artist: "",
  category: "mixing-comparison",
  projectId: "",
  beforeUrl: "",
  afterUrl: "",
  description: "",
  tags: "",
  year: "",
};

export default function ManageAudioClient() {
  const { fadeInUp, staggerChildren } = useAnimations();
  const [comparisons, setComparisons] = useState<AudioComparison[]>([]);
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [urlValidation, setUrlValidation] = useState<{
    beforeUrl: AudioValidationResult & { isChecking: boolean };
    afterUrl: AudioValidationResult & { isChecking: boolean };
  }>({
    beforeUrl: { isValid: false, isChecking: false },
    afterUrl: { isValid: false, isChecking: false },
  });
  const [previewAudio, setPreviewAudio] = useState<{
    beforeUrl: AudioPreview | null;
    afterUrl: AudioPreview | null;
  }>({
    beforeUrl: null,
    afterUrl: null,
  });
  const [playingStates, setPlayingStates] = useState<{
    [key: string]: boolean;
  }>({});

  // Load existing comparisons
  useEffect(() => {
    loadComparisons();
  }, []);

  // Helper function to handle audio preview with play/pause toggle
  const handleAudioPreview = async (url: string, audioId: string) => {
    if (!url) return;

    const isCurrentlyPlaying = playingStates[audioId];

    if (isCurrentlyPlaying) {
      // If currently playing, pause it
      setPlayingStates((prev) => ({ ...prev, [audioId]: false }));
      return;
    }

    // Create new audio preview if needed
    const preview = new AudioPreview(
      undefined, // onTimeUpdate
      () => {
        // onEnded
        setPlayingStates((prev) => ({ ...prev, [audioId]: false }));
      },
      (isPlaying: boolean) => {
        // onPlayStateChange
        setPlayingStates((prev) => ({ ...prev, [audioId]: isPlaying }));
      }
    );

    try {
      const loaded = await preview.load(url);
      if (loaded) {
        await preview.play();
      } else {
        console.error("Failed to load audio:", url);
      }
    } catch (error) {
      console.error("Error playing audio:", error);
      setPlayingStates((prev) => ({ ...prev, [audioId]: false }));
    }
  };

  const loadComparisons = async () => {
    try {
      const data = await audioComparisonManager.getAll();
      setComparisons(data);
    } catch (error) {
      console.error("Error loading comparisons:", error);
    }
  };

  const validateAudioUrlField = async (
    url: string,
    type: "beforeUrl" | "afterUrl"
  ) => {
    if (!url.trim()) {
      setUrlValidation((prev) => ({
        ...prev,
        [type]: { isValid: false, isChecking: false },
      }));
      return;
    }

    setUrlValidation((prev) => ({
      ...prev,
      [type]: { isValid: false, isChecking: true },
    }));

    try {
      const result = await validateAudioUrl(url);
      setUrlValidation((prev) => ({
        ...prev,
        [type]: { ...result, isChecking: false },
      }));
    } catch (error) {
      setUrlValidation((prev) => ({
        ...prev,
        [type]: {
          isValid: false,
          isChecking: false,
          error: "Validation failed",
        },
      }));
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Validate URLs when they change
    if (field === "beforeUrl" || field === "afterUrl") {
      validateAudioUrlField(value, field);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const comparisonData: CreateComparisonData = {
        title: formData.title,
        artist: formData.artist,
        category: formData.category,
        projectId: formData.projectId,
        beforeUrl: formData.beforeUrl,
        afterUrl: formData.afterUrl,
        description: formData.description,
        year: formData.year ? parseInt(formData.year) : undefined,
        tags: formData.tags
          .split(",")
          .map((tag) => tag.trim())
          .filter(Boolean),
      };

      // Validate data before submission
      const validation =
        audioComparisonManager.validateComparisonData(comparisonData);
      if (!validation.isValid) {
        alert(`Validation errors:\n${validation.errors.join("\n")}`);
        return;
      }

      if (editingId) {
        await audioComparisonManager.update(editingId, comparisonData);
      } else {
        await audioComparisonManager.create(comparisonData);
      }

      await loadComparisons();
      resetForm();
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error saving comparison:", error);
      alert("Error saving comparison. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (comparison: AudioComparison) => {
    setFormData({
      title: comparison.title,
      artist: comparison.artist,
      category: comparison.category,
      projectId: comparison.beforeAudio.projectId,
      beforeUrl: comparison.beforeAudio.url,
      afterUrl: comparison.afterAudio.url,
      description: comparison.description || "",
      tags: comparison.tags?.join(", ") || "",
      year: comparison.year?.toString() || "",
    });
    setEditingId(comparison.id);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this comparison?")) return;

    try {
      await audioComparisonManager.delete(id);
      await loadComparisons();
    } catch (error) {
      console.error("Error deleting comparison:", error);
      alert("Error deleting comparison. Please try again.");
    }
  };

  const resetForm = () => {
    setFormData(initialFormData);
    setEditingId(null);
    setIsDialogOpen(false);
    setUrlValidation({
      beforeUrl: { isValid: false, isChecking: false },
      afterUrl: { isValid: false, isChecking: false },
    });
    // Cleanup preview audio
    if (previewAudio.beforeUrl) {
      previewAudio.beforeUrl.cleanup();
    }
    if (previewAudio.afterUrl) {
      previewAudio.afterUrl.cleanup();
    }
    setPreviewAudio({
      beforeUrl: null,
      afterUrl: null,
    });
    // Reset playing states for form
    setPlayingStates((prev) => ({
      ...prev,
      "form-before": false,
      "form-after": false,
    }));
  };

  const getVersionLabel = (category: string, type: "before" | "after") => {
    if (category === "mixing-comparison") {
      return type === "before" ? "Unmixed" : "Mixed";
    }
    return type === "before" ? "Unmastered" : "Mastered";
  };

  return (
    <div className="container mx-auto px-4 py-16 sm:py-24">
      <motion.div
        className="max-w-6xl mx-auto"
        initial="hidden"
        animate="visible"
        variants={staggerChildren}
      >
        {/* Header */}
        <motion.div variants={fadeInUp} className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Audio Comparison Manager</h1>
          <p className="text-xl text-gray-300">
            Manage audio comparisons using Vercel Blob URLs
          </p>
        </motion.div>

        {/* Add New Button */}
        <motion.div variants={fadeInUp} className="mb-8">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={resetForm}
                className="bg-orange-500 hover:bg-orange-600"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add New Comparison
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingId ? "Edit" : "Add New"} Audio Comparison
                </DialogTitle>
                <DialogDescription>
                  Enter the details and Vercel Blob URLs for your audio
                  comparison
                </DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) =>
                        handleInputChange("title", e.target.value)
                      }
                      placeholder="Song title"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="artist">Artist</Label>
                    <Input
                      id="artist"
                      value={formData.artist}
                      onChange={(e) =>
                        handleInputChange("artist", e.target.value)
                      }
                      placeholder="Artist name"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(
                        value: "mixing-comparison" | "mastering-comparison"
                      ) => handleInputChange("category", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mixing-comparison">
                          Mixing Comparison
                        </SelectItem>
                        <SelectItem value="mastering-comparison">
                          Mastering Comparison
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="projectId">Project ID</Label>
                    <Input
                      id="projectId"
                      value={formData.projectId}
                      onChange={(e) =>
                        handleInputChange("projectId", e.target.value)
                      }
                      placeholder="unique-project-id"
                      required
                    />
                  </div>
                </div>

                {/* URL Inputs with Validation */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="beforeUrl">
                      {getVersionLabel(formData.category, "before")} Audio URL
                    </Label>
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <Input
                          id="beforeUrl"
                          value={formData.beforeUrl}
                          onChange={(e) =>
                            handleInputChange("beforeUrl", e.target.value)
                          }
                          placeholder="https://your-blob-url.com/audio.mp3"
                          required
                          className={cn(
                            urlValidation.beforeUrl.isValid &&
                              "border-green-500",
                            formData.beforeUrl &&
                              !urlValidation.beforeUrl.isValid &&
                              !urlValidation.beforeUrl.isChecking &&
                              "border-red-500"
                          )}
                        />
                        {urlValidation.beforeUrl.isChecking && (
                          <div className="flex items-center px-3">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"></div>
                          </div>
                        )}
                        {formData.beforeUrl &&
                          !urlValidation.beforeUrl.isChecking && (
                            <div className="flex items-center px-3">
                              {urlValidation.beforeUrl.isValid ? (
                                <CheckCircle className="w-4 h-4 text-green-500" />
                              ) : (
                                <AlertCircle className="w-4 h-4 text-red-500" />
                              )}
                            </div>
                          )}
                        {urlValidation.beforeUrl.isValid && (
                          <Button
                            type="button"
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              handleAudioPreview(
                                formData.beforeUrl,
                                "form-before"
                              )
                            }
                            className="px-3"
                          >
                            {playingStates["form-before"] ? (
                              <Pause className="w-4 h-4" />
                            ) : (
                              <Play className="w-4 h-4" />
                            )}
                          </Button>
                        )}
                      </div>
                      {urlValidation.beforeUrl.error && (
                        <p className="text-sm text-red-400">
                          {urlValidation.beforeUrl.error}
                        </p>
                      )}
                      {urlValidation.beforeUrl.isValid &&
                        urlValidation.beforeUrl.duration && (
                          <p className="text-sm text-green-400">
                            Duration:{" "}
                            {formatDuration(urlValidation.beforeUrl.duration)}
                            {urlValidation.beforeUrl.format &&
                              ` • Format: ${urlValidation.beforeUrl.format}`}
                          </p>
                        )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="afterUrl">
                      {getVersionLabel(formData.category, "after")} Audio URL
                    </Label>
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <Input
                          id="afterUrl"
                          value={formData.afterUrl}
                          onChange={(e) =>
                            handleInputChange("afterUrl", e.target.value)
                          }
                          placeholder="https://your-blob-url.com/audio.mp3"
                          required
                          className={cn(
                            urlValidation.afterUrl.isValid &&
                              "border-green-500",
                            formData.afterUrl &&
                              !urlValidation.afterUrl.isValid &&
                              !urlValidation.afterUrl.isChecking &&
                              "border-red-500"
                          )}
                        />
                        {urlValidation.afterUrl.isChecking && (
                          <div className="flex items-center px-3">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"></div>
                          </div>
                        )}
                        {formData.afterUrl &&
                          !urlValidation.afterUrl.isChecking && (
                            <div className="flex items-center px-3">
                              {urlValidation.afterUrl.isValid ? (
                                <CheckCircle className="w-4 h-4 text-green-500" />
                              ) : (
                                <AlertCircle className="w-4 h-4 text-red-500" />
                              )}
                            </div>
                          )}
                        {urlValidation.afterUrl.isValid && (
                          <Button
                            type="button"
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              handleAudioPreview(
                                formData.afterUrl,
                                "form-after"
                              )
                            }
                            className="px-3"
                          >
                            {playingStates["form-after"] ? (
                              <Pause className="w-4 h-4" />
                            ) : (
                              <Play className="w-4 h-4" />
                            )}
                          </Button>
                        )}
                      </div>
                      {urlValidation.afterUrl.error && (
                        <p className="text-sm text-red-400">
                          {urlValidation.afterUrl.error}
                        </p>
                      )}
                      {urlValidation.afterUrl.isValid &&
                        urlValidation.afterUrl.duration && (
                          <p className="text-sm text-green-400">
                            Duration:{" "}
                            {formatDuration(urlValidation.afterUrl.duration)}
                            {urlValidation.afterUrl.format &&
                              ` • Format: ${urlValidation.afterUrl.format}`}
                          </p>
                        )}
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) =>
                      handleInputChange("description", e.target.value)
                    }
                    placeholder="Brief description of the comparison"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="year">Year (Optional)</Label>
                    <Input
                      id="year"
                      type="number"
                      value={formData.year}
                      onChange={(e) =>
                        handleInputChange("year", e.target.value)
                      }
                      placeholder="2024"
                    />
                  </div>
                  <div>
                    <Label htmlFor="tags">Tags (comma-separated)</Label>
                    <Input
                      id="tags"
                      value={formData.tags}
                      onChange={(e) =>
                        handleInputChange("tags", e.target.value)
                      }
                      placeholder="hip-hop, collaboration, energetic"
                    />
                  </div>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button
                    type="submit"
                    disabled={
                      isLoading ||
                      !urlValidation.beforeUrl.isValid ||
                      !urlValidation.afterUrl.isValid
                    }
                    className="bg-orange-500 hover:bg-orange-600"
                  >
                    {isLoading ? "Saving..." : editingId ? "Update" : "Add"}{" "}
                    Comparison
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </motion.div>

        {/* Existing Comparisons Table */}
        <motion.div variants={fadeInUp}>
          <Card className="border-white/10 bg-black/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Existing Audio Comparisons</CardTitle>
              <CardDescription>
                Manage your audio comparison library
              </CardDescription>
            </CardHeader>
            <CardContent>
              {comparisons.length === 0 ? (
                <div className="text-center py-8 text-gray-400">
                  No audio comparisons found. Add your first comparison above.
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Artist</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Before Audio</TableHead>
                        <TableHead>After Audio</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {comparisons.map((comparison) => (
                        <TableRow key={comparison.id}>
                          <TableCell className="font-medium">
                            {comparison.title}
                          </TableCell>
                          <TableCell>{comparison.artist}</TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {comparison.category === "mixing-comparison"
                                ? "Mixing"
                                : "Mastering"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() =>
                                  handleAudioPreview(
                                    comparison.beforeAudio.url,
                                    `table-before-${comparison.id}`
                                  )
                                }
                                className="h-8 w-8 p-0"
                              >
                                {playingStates[
                                  `table-before-${comparison.id}`
                                ] ? (
                                  <Pause className="w-3 h-3" />
                                ) : (
                                  <Play className="w-3 h-3" />
                                )}
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() =>
                                  window.open(
                                    comparison.beforeAudio.url,
                                    "_blank"
                                  )
                                }
                                className="h-8 w-8 p-0"
                              >
                                <ExternalLink className="w-3 h-3" />
                              </Button>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() =>
                                  handleAudioPreview(
                                    comparison.afterAudio.url,
                                    `table-after-${comparison.id}`
                                  )
                                }
                                className="h-8 w-8 p-0"
                              >
                                {playingStates[
                                  `table-after-${comparison.id}`
                                ] ? (
                                  <Pause className="w-3 h-3" />
                                ) : (
                                  <Play className="w-3 h-3" />
                                )}
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() =>
                                  window.open(
                                    comparison.afterAudio.url,
                                    "_blank"
                                  )
                                }
                                className="h-8 w-8 p-0"
                              >
                                <ExternalLink className="w-3 h-3" />
                              </Button>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleEdit(comparison)}
                                className="h-8 w-8 p-0"
                              >
                                <Edit className="w-3 h-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleDelete(comparison.id)}
                                className="h-8 w-8 p-0 text-red-400 hover:text-red-300"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </div>
  );
}

const CACHE_DURATION = 1000 * 60 * 60; // 1 hour in milliseconds
const BASE_CURRENCY = 'USD';

interface ExchangeRateCache {
  rates: Record<string, number>;
  timestamp: number;
}

let rateCache: ExchangeRateCache | null = null;

export async function getExchangeRates(): Promise<Record<string, number>> {
  // Check if we have valid cached rates
  if (rateCache && Date.now() - rateCache.timestamp < CACHE_DURATION) {
    return rateCache.rates;
  }

  try {
    const response = await fetch(
      `https://openexchangerates.org/api/latest.json?app_id=${process.env.NEXT_PUBLIC_OPEN_EXCHANGE_RATES_APP_ID}&base=${BASE_CURRENCY}`
    );

    if (!response.ok) {
      throw new Error('Failed to fetch exchange rates');
    }

    const data = await response.json();
    
    // Update cache
    rateCache = {
      rates: data.rates,
      timestamp: Date.now(),
    };

    return data.rates;
  } catch (error) {
    console.error('Error fetching exchange rates:', error);
    // Return cached rates if available, even if expired
    if (rateCache) {
      return rateCache.rates;
    }
    // Fallback to hardcoded rate if no cache available
    return {
      ZAR: 18.5, // Fallback rate
    };
  }
}

export function convertCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string,
  rates: Record<string, number>
): number {
  if (fromCurrency === toCurrency) return amount;
  
  // Convert to USD first (base currency)
  const amountInUSD = fromCurrency === BASE_CURRENCY 
    ? amount 
    : amount / rates[fromCurrency];
  
  // Convert from USD to target currency
  return toCurrency === BASE_CURRENCY 
    ? amountInUSD 
    : amountInUSD * rates[toCurrency];
} 
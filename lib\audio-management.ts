/**
 * Client-side audio comparison management utilities
 */

import { AudioComparison } from "./audio-data";

export interface CreateComparisonData {
  title: string;
  artist: string;
  category: "mixing-comparison" | "mastering-comparison";
  projectId: string;
  beforeUrl: string;
  afterUrl: string;
  description?: string;
  year?: number;
  tags?: string[];
}

export interface UpdateComparisonData extends Partial<CreateComparisonData> {
  id: string;
}

export class AudioComparisonManager {
  private baseUrl = "/api/audio/comparisons";

  /**
   * Get all audio comparisons
   */
  async getAll(): Promise<AudioComparison[]> {
    try {
      const response = await fetch(this.baseUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch comparisons: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error("Error fetching comparisons:", error);
      throw error;
    }
  }

  /**
   * Get a specific audio comparison by ID
   */
  async getById(id: string): Promise<AudioComparison> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Comparison not found");
        }
        throw new Error(`Failed to fetch comparison: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error("Error fetching comparison:", error);
      throw error;
    }
  }

  /**
   * Create a new audio comparison
   */
  async create(data: CreateComparisonData): Promise<AudioComparison> {
    try {
      // Generate a unique ID
      const id = `comparison-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const comparisonData = this.formatComparisonData(id, data);
      
      const response = await fetch(this.baseUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(comparisonData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to create comparison: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error creating comparison:", error);
      throw error;
    }
  }

  /**
   * Update an existing audio comparison
   */
  async update(id: string, data: Partial<CreateComparisonData>): Promise<AudioComparison> {
    try {
      // Get the existing comparison to merge with updates
      const existing = await this.getById(id);
      
      const updatedData = {
        ...existing,
        ...data,
      };
      
      const comparisonData = this.formatComparisonData(id, updatedData);
      
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(comparisonData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to update comparison: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error updating comparison:", error);
      throw error;
    }
  }

  /**
   * Delete an audio comparison
   */
  async delete(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to delete comparison: ${response.statusText}`);
      }
    } catch (error) {
      console.error("Error deleting comparison:", error);
      throw error;
    }
  }

  /**
   * Search comparisons by various criteria
   */
  async search(criteria: {
    title?: string;
    artist?: string;
    category?: "mixing-comparison" | "mastering-comparison";
    tags?: string[];
  }): Promise<AudioComparison[]> {
    try {
      const comparisons = await this.getAll();
      
      return comparisons.filter(comparison => {
        if (criteria.title && !comparison.title.toLowerCase().includes(criteria.title.toLowerCase())) {
          return false;
        }
        
        if (criteria.artist && !comparison.artist.toLowerCase().includes(criteria.artist.toLowerCase())) {
          return false;
        }
        
        if (criteria.category && comparison.category !== criteria.category) {
          return false;
        }
        
        if (criteria.tags && criteria.tags.length > 0) {
          const comparisonTags = comparison.tags || [];
          const hasMatchingTag = criteria.tags.some(tag => 
            comparisonTags.some(compTag => 
              compTag.toLowerCase().includes(tag.toLowerCase())
            )
          );
          if (!hasMatchingTag) {
            return false;
          }
        }
        
        return true;
      });
    } catch (error) {
      console.error("Error searching comparisons:", error);
      throw error;
    }
  }

  /**
   * Get comparisons by category
   */
  async getByCategory(category: "mixing-comparison" | "mastering-comparison"): Promise<AudioComparison[]> {
    return this.search({ category });
  }

  /**
   * Format comparison data for API submission
   */
  private formatComparisonData(id: string, data: CreateComparisonData | AudioComparison): any {
    const beforeType = data.category === "mixing-comparison" ? "unmixed" : "unmastered";
    const afterType = data.category === "mixing-comparison" ? "mixed" : "mastered";
    
    return {
      id,
      title: data.title,
      artist: data.artist,
      category: data.category,
      beforeAudio: {
        id: `${data.projectId}-before`,
        url: data.beforeUrl,
        title: data.title,
        artist: data.artist,
        type: beforeType,
        category: data.category,
        projectId: data.projectId,
        uploadedAt: new Date().toISOString(),
        tags: data.tags || [],
      },
      afterAudio: {
        id: `${data.projectId}-after`,
        url: data.afterUrl,
        title: data.title,
        artist: data.artist,
        type: afterType,
        category: data.category,
        projectId: data.projectId,
        uploadedAt: new Date().toISOString(),
        tags: data.tags || [],
      },
      description: data.description,
      year: data.year,
      tags: data.tags || [],
    };
  }

  /**
   * Validate comparison data before submission
   */
  validateComparisonData(data: CreateComparisonData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.title?.trim()) {
      errors.push("Title is required");
    }

    if (!data.artist?.trim()) {
      errors.push("Artist is required");
    }

    if (!data.projectId?.trim()) {
      errors.push("Project ID is required");
    }

    if (!data.beforeUrl?.trim()) {
      errors.push("Before audio URL is required");
    }

    if (!data.afterUrl?.trim()) {
      errors.push("After audio URL is required");
    }

    if (!["mixing-comparison", "mastering-comparison"].includes(data.category)) {
      errors.push("Valid category is required");
    }

    // Validate URLs format
    if (data.beforeUrl) {
      try {
        new URL(data.beforeUrl);
      } catch {
        errors.push("Before audio URL is not a valid URL");
      }
    }

    if (data.afterUrl) {
      try {
        new URL(data.afterUrl);
      } catch {
        errors.push("After audio URL is not a valid URL");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// Export a singleton instance
export const audioComparisonManager = new AudioComparisonManager();

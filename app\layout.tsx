import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/Navigation";
import { PageTransition } from "@/components/PageTransition";
import { WaveformBackground } from "@/components/WaveformBackground";
import { Toaster } from "sonner";
import { Analytics } from "@vercel/analytics/next";
import {
  getSEOConfig,
  getCanonicalUrl,
  getOrganizationStructuredData,
} from "@/lib/seo-config";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Default metadata - will be overridden by page-specific metadata
export const metadata: Metadata = (() => {
  const config = getSEOConfig("home");
  return {
    metadataBase: new URL("https://noizecapital.com"),
    title: {
      template: "%s | Noize Capital",
      default: config.title,
    },
    description: config.description,
    keywords: config.keywords,
    authors: [{ name: "Noize Capital" }],
    creator: "Noize Capital",
    publisher: "Noize Capital",
    alternates: {
      canonical: getCanonicalUrl("/"),
    },
    openGraph: {
      type: "website",
      locale: "en_US",
      url: getCanonicalUrl("/"),
      siteName: "Noize Capital",
      title: config.openGraph?.title || config.title,
      description: config.openGraph?.description || config.description,
      images: config.openGraph?.image
        ? [
            {
              url: config.openGraph.image,
              width: 1200,
              height: 630,
              alt: "Noize Capital - Professional Mixing & Mastering Services",
            },
          ]
        : undefined,
    },
    twitter: {
      card: "summary_large_image",
      title: config.title,
      description: config.description,
      images: config.openGraph?.image ? [config.openGraph.image] : undefined,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
})();

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(getOrganizationStructuredData()),
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-black text-white min-h-screen`}
      >
        <WaveformBackground />
        <Navigation />
        <PageTransition>
          <main className="pt-16">{children}</main>
        </PageTransition>
        <Analytics />
        <Toaster position="top-right" richColors />
      </body>
    </html>
  );
}

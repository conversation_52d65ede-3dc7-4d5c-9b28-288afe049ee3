import { test, expect } from '@playwright/test';

test.describe('Audio Portfolio System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the portfolio page before each test
    await page.goto('/portfolio');
  });

  test('should display portfolio page with audio comparisons tab', async ({ page }) => {
    // Check that the page loads correctly
    await expect(page.locator('h1')).toContainText('Our Portfolio');
    
    // Check that the tabs are present
    await expect(page.locator('[role="tablist"]')).toBeVisible();
    await expect(page.getByRole('tab', { name: /Audio Comparisons/i })).toBeVisible();
    await expect(page.getByRole('tab', { name: /Spotify Releases/i })).toBeVisible();
    
    // Audio Comparisons tab should be active by default
    await expect(page.getByRole('tab', { name: /Audio Comparisons/i })).toHaveAttribute('data-state', 'active');
  });

  test('should display statistics overview', async ({ page }) => {
    // Check that statistics cards are visible
    await expect(page.locator('text=Total Projects')).toBeVisible();
    await expect(page.locator('text=Mixing Examples')).toBeVisible();
    await expect(page.locator('text=Mastering Examples')).toBeVisible();
    await expect(page.locator('text=Years Active')).toBeVisible();
    
    // Check that statistics have numeric values
    const totalProjects = page.locator('text=Total Projects').locator('..').locator('.text-2xl');
    await expect(totalProjects).toBeVisible();
  });

  test('should display mixing and mastering comparison sections', async ({ page }) => {
    // Check for mixing comparisons section
    await expect(page.locator('h2', { hasText: 'Mixing Comparisons' })).toBeVisible();
    await expect(page.locator('text=Hear the difference professional mixing makes')).toBeVisible();
    
    // Check for mastering comparisons section
    await expect(page.locator('h2', { hasText: 'Mastering Comparisons' })).toBeVisible();
    await expect(page.locator('text=Experience the final polish')).toBeVisible();
  });

  test('should have waveform toggle functionality', async ({ page }) => {
    // Check that waveform toggle button exists
    const waveformToggle = page.getByRole('button', { name: /Hide Waveforms|Show Waveforms/i });
    await expect(waveformToggle).toBeVisible();
    
    // Click the toggle button
    const initialText = await waveformToggle.textContent();
    await waveformToggle.click();
    
    // Wait for the button text to change
    await expect(waveformToggle).not.toHaveText(initialText || '');
  });

  test('should switch between tabs correctly', async ({ page }) => {
    // Start on Audio Comparisons tab
    await expect(page.getByRole('tab', { name: /Audio Comparisons/i })).toHaveAttribute('data-state', 'active');
    
    // Click on Spotify Releases tab
    await page.getByRole('tab', { name: /Spotify Releases/i }).click();
    
    // Check that Spotify tab is now active
    await expect(page.getByRole('tab', { name: /Spotify Releases/i })).toHaveAttribute('data-state', 'active');
    await expect(page.getByRole('tab', { name: /Audio Comparisons/i })).toHaveAttribute('data-state', 'inactive');
    
    // Check that Spotify content is visible
    await expect(page.locator('h2', { hasText: 'Spotify Releases' })).toBeVisible();
    
    // Switch back to Audio Comparisons
    await page.getByRole('tab', { name: /Audio Comparisons/i }).click();
    await expect(page.getByRole('tab', { name: /Audio Comparisons/i })).toHaveAttribute('data-state', 'active');
  });

  test('should display call to action section', async ({ page }) => {
    // Scroll to bottom to see CTA
    await page.locator('text=Ready to create your next project?').scrollIntoViewIfNeeded();
    
    // Check CTA elements
    await expect(page.locator('text=Ready to create your next project?')).toBeVisible();
    await expect(page.locator('text=Let\'s collaborate to bring your musical vision to life')).toBeVisible();
    await expect(page.getByRole('link', { name: /Start Your Project/i })).toBeVisible();
  });

  test('should have responsive design elements', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that main elements are still visible on mobile
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.getByRole('tablist')).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('h1')).toBeVisible();
    
    // Reset to desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
  });
});

test.describe('Admin Audio Upload System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the admin audio upload page
    await page.goto('/admin/audio');
  });

  test('should display audio upload interface', async ({ page }) => {
    // Check page title and description
    await expect(page.locator('h1')).toContainText('Audio Upload Manager');
    await expect(page.locator('text=Upload and manage audio files for portfolio comparisons')).toBeVisible();
    
    // Check form sections
    await expect(page.locator('text=Audio Metadata')).toBeVisible();
    await expect(page.locator('text=Upload Files')).toBeVisible();
  });

  test('should have all required form fields', async ({ page }) => {
    // Check metadata form fields
    await expect(page.locator('label', { hasText: 'Title' })).toBeVisible();
    await expect(page.locator('label', { hasText: 'Artist' })).toBeVisible();
    await expect(page.locator('label', { hasText: 'Audio Type' })).toBeVisible();
    await expect(page.locator('label', { hasText: 'Category' })).toBeVisible();
    await expect(page.locator('label', { hasText: 'Project ID' })).toBeVisible();
    await expect(page.locator('label', { hasText: 'Tags' })).toBeVisible();
    
    // Check input fields are functional
    await page.fill('input[placeholder="Song title"]', 'Test Song');
    await page.fill('input[placeholder="Artist name"]', 'Test Artist');
    await page.fill('input[placeholder="unique-project-identifier"]', 'test-project-123');
    await page.fill('input[placeholder*="hip-hop"]', 'test, demo, sample');
    
    // Verify values were entered
    await expect(page.locator('input[placeholder="Song title"]')).toHaveValue('Test Song');
    await expect(page.locator('input[placeholder="Artist name"]')).toHaveValue('Test Artist');
  });

  test('should have working dropdown selectors', async ({ page }) => {
    // Test Audio Type selector
    await page.click('[role="combobox"]', { timeout: 5000 });
    await expect(page.locator('text=Mixed')).toBeVisible();
    await expect(page.locator('text=Unmixed')).toBeVisible();
    await expect(page.locator('text=Mastered')).toBeVisible();
    await expect(page.locator('text=Unmastered')).toBeVisible();
    
    // Select an option
    await page.click('text=Mixed');
    
    // Test Category selector (click the second combobox)
    await page.locator('[role="combobox"]').nth(1).click();
    await expect(page.locator('text=Mixing Comparison')).toBeVisible();
    await expect(page.locator('text=Mastering Comparison')).toBeVisible();
  });

  test('should display file upload area', async ({ page }) => {
    // Check upload area
    await expect(page.locator('text=Drop audio files here or click to browse')).toBeVisible();
    await expect(page.locator('text=Supports MP3, WAV, M4A, AAC (max 50MB each)')).toBeVisible();
    
    // Check upload icon is present
    await expect(page.locator('svg').first()).toBeVisible();
  });
});

test.describe('Admin File Management System', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/admin/manage');
  });

  test('should display file management interface', async ({ page }) => {
    // Check page elements
    await expect(page.locator('h1')).toContainText('Manage Audio Files');
    await expect(page.locator('text=View, search, and manage your uploaded audio files')).toBeVisible();
    
    // Check search functionality
    await expect(page.locator('input[placeholder="Search files..."]')).toBeVisible();
    await expect(page.getByRole('button', { name: /Refresh/i })).toBeVisible();
    
    // Check statistics cards
    await expect(page.locator('text=Total Files')).toBeVisible();
    await expect(page.locator('text=Total Size')).toBeVisible();
    await expect(page.locator('text=Mixing Files')).toBeVisible();
  });

  test('should handle empty state gracefully', async ({ page }) => {
    // If no files are uploaded, should show appropriate message
    const noFilesMessage = page.locator('text=No audio files uploaded yet');
    const filesTable = page.locator('table');
    
    // Either show empty state or files table
    const hasFiles = await filesTable.isVisible();
    if (!hasFiles) {
      await expect(noFilesMessage).toBeVisible();
    }
  });

  test('should have functional search', async ({ page }) => {
    const searchInput = page.locator('input[placeholder="Search files..."]');
    
    // Test search functionality
    await searchInput.fill('test');
    await expect(searchInput).toHaveValue('test');
    
    // Clear search
    await searchInput.clear();
    await expect(searchInput).toHaveValue('');
  });
});

test.describe('Admin Navigation', () => {
  test('should navigate between admin pages', async ({ page }) => {
    // Start at admin dashboard
    await page.goto('/admin');
    
    // Check dashboard elements
    await expect(page.locator('h1')).toContainText('Admin Dashboard');
    
    // Navigate to audio upload
    await page.getByRole('link', { name: /Go to Upload/i }).click();
    await expect(page).toHaveURL('/admin/audio');
    await expect(page.locator('h1')).toContainText('Audio Upload Manager');
    
    // Navigate to file management via nav
    await page.getByRole('button', { name: /Manage Files/i }).click();
    await expect(page).toHaveURL('/admin/manage');
    await expect(page.locator('h1')).toContainText('Manage Audio Files');
    
    // Navigate back to main site
    await page.getByRole('button', { name: /Back to Site/i }).click();
    await expect(page).toHaveURL('/');
  });

  test('should have consistent admin layout', async ({ page }) => {
    await page.goto('/admin');
    
    // Check admin navigation elements
    await expect(page.locator('text=Admin Panel')).toBeVisible();
    await expect(page.getByRole('button', { name: /Back to Site/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /Audio Upload/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /Manage Files/i })).toBeVisible();
  });
});

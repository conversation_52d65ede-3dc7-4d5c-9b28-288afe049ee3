import { test, expect } from "@playwright/test";

test.describe("Audio Portfolio System", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the portfolio page before each test
    await page.goto("/portfolio");
  });

  test("should display portfolio page with audio comparisons tab", async ({
    page,
  }) => {
    // Check that the page loads correctly
    await expect(page.locator("h1")).toContainText("Our Portfolio");

    // Check that the tabs are present
    await expect(page.locator('[role="tablist"]')).toBeVisible();
    await expect(
      page.getByRole("tab", { name: /Audio Comparisons/i })
    ).toBeVisible();
    await expect(
      page.getByRole("tab", { name: /Spotify Releases/i })
    ).toBeVisible();

    // Audio Comparisons tab should be active by default
    await expect(
      page.getByRole("tab", { name: /Audio Comparisons/i })
    ).toHaveAttribute("data-state", "active");
  });

  test("should display statistics overview", async ({ page }) => {
    // Check that statistics cards are visible
    await expect(page.locator("text=Total Projects")).toBeVisible();
    await expect(page.locator("text=Mixing Examples")).toBeVisible();
    await expect(page.locator("text=Mastering Examples")).toBeVisible();
    await expect(page.locator("text=Years Active")).toBeVisible();

    // Check that statistics have numeric values
    const totalProjects = page
      .locator("text=Total Projects")
      .locator("..")
      .locator(".text-2xl");
    await expect(totalProjects).toBeVisible();
  });

  test("should display mixing and mastering comparison sections", async ({
    page,
  }) => {
    // Check for mixing comparisons section
    await expect(
      page.locator("h2", { hasText: "Mixing Comparisons" })
    ).toBeVisible();
    await expect(
      page.locator("text=Hear the difference professional mixing makes")
    ).toBeVisible();

    // Check for mastering comparisons section
    await expect(
      page.locator("h2", { hasText: "Mastering Comparisons" })
    ).toBeVisible();
    await expect(
      page.locator("text=Experience the final polish")
    ).toBeVisible();
  });

  test("should have waveform toggle functionality", async ({ page }) => {
    // Check that waveform toggle button exists
    const waveformToggle = page.getByRole("button", {
      name: /Hide Waveforms|Show Waveforms/i,
    });
    await expect(waveformToggle).toBeVisible();

    // Click the toggle button
    const initialText = await waveformToggle.textContent();
    await waveformToggle.click();

    // Wait for the button text to change
    await expect(waveformToggle).not.toHaveText(initialText || "");
  });

  test("should switch between tabs correctly", async ({ page }) => {
    // Start on Audio Comparisons tab
    await expect(
      page.getByRole("tab", { name: /Audio Comparisons/i })
    ).toHaveAttribute("data-state", "active");

    // Click on Spotify Releases tab
    await page.getByRole("tab", { name: /Spotify Releases/i }).click();

    // Check that Spotify tab is now active
    await expect(
      page.getByRole("tab", { name: /Spotify Releases/i })
    ).toHaveAttribute("data-state", "active");
    await expect(
      page.getByRole("tab", { name: /Audio Comparisons/i })
    ).toHaveAttribute("data-state", "inactive");

    // Check that Spotify content is visible
    await expect(
      page.locator("h2", { hasText: "Spotify Releases" })
    ).toBeVisible();

    // Switch back to Audio Comparisons
    await page.getByRole("tab", { name: /Audio Comparisons/i }).click();
    await expect(
      page.getByRole("tab", { name: /Audio Comparisons/i })
    ).toHaveAttribute("data-state", "active");
  });

  test("should display call to action section", async ({ page }) => {
    // Scroll to bottom to see CTA
    await page
      .locator("text=Ready to create your next project?")
      .scrollIntoViewIfNeeded();

    // Check CTA elements
    await expect(
      page.locator("text=Ready to create your next project?")
    ).toBeVisible();
    await expect(
      page.locator(
        "text=Let's collaborate to bring your musical vision to life"
      )
    ).toBeVisible();
    await expect(
      page.getByRole("link", { name: /Start Your Project/i })
    ).toBeVisible();
  });

  test("should have responsive design elements", async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Check that main elements are still visible on mobile
    await expect(page.locator("h1")).toBeVisible();
    await expect(page.getByRole("tablist")).toBeVisible();

    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator("h1")).toBeVisible();

    // Reset to desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
  });

  test("should display loading states for audio comparisons", async ({
    page,
  }) => {
    // Wait for page to load
    await page.waitForLoadState("networkidle");

    // Check for loading indicators during initial load
    const loadingIndicators = page.locator("text=Loading audio...");
    const progressBars = page.locator(".bg-orange-500");

    // If audio is still loading, verify loading UI
    if (await loadingIndicators.first().isVisible({ timeout: 1000 })) {
      await expect(loadingIndicators.first()).toBeVisible();
      await expect(progressBars.first()).toBeVisible();
    }

    // Wait for audio to finish loading
    await page.waitForFunction(
      () => {
        const loadingTexts = document.querySelectorAll("*");
        for (const el of loadingTexts) {
          if (el.textContent?.includes("Loading audio...")) {
            return false;
          }
        }
        return true;
      },
      {},
      { timeout: 10000 }
    );
  });

  test("should handle audio version switching correctly", async ({ page }) => {
    // Wait for page to load completely
    await page.waitForLoadState("networkidle");

    // Wait for audio to finish loading
    await page.waitForFunction(
      () => {
        const loadingTexts = document.querySelectorAll("*");
        for (const el of loadingTexts) {
          if (el.textContent?.includes("Loading audio...")) {
            return false;
          }
        }
        return true;
      },
      {},
      { timeout: 10000 }
    );

    // Find audio comparison components
    const audioComparisons = page
      .locator('[data-testid="audio-comparison"]')
      .or(page.locator(".space-y-6 > div").filter({ hasText: "Unmixed" }));

    if (await audioComparisons.first().isVisible()) {
      const firstComparison = audioComparisons.first();

      // Check for version toggle buttons
      const unmixedButton = firstComparison.getByRole("button", {
        name: "Unmixed",
        exact: true,
      });
      const mixedButton = firstComparison.getByRole("button", {
        name: "Mixed",
        exact: true,
      });

      if (
        (await unmixedButton.isVisible()) &&
        (await mixedButton.isVisible())
      ) {
        // Test switching between versions
        await unmixedButton.click();
        await expect(unmixedButton).toHaveClass(/bg-orange-500/);

        await mixedButton.click();
        await expect(mixedButton).toHaveClass(/bg-orange-500/);

        // Verify play button remains functional after switching
        const playButton = firstComparison
          .locator("button")
          .filter({ hasText: /Play|Pause/ })
          .first();
        if (await playButton.isVisible()) {
          await expect(playButton).toBeEnabled();
        }
      }
    }
  });

  test("should display waveform visualizations when enabled", async ({
    page,
  }) => {
    // Wait for page to load
    await page.waitForLoadState("networkidle");

    // Ensure waveforms are shown
    const waveformToggle = page.getByRole("button", {
      name: /Show Waveforms/i,
    });
    if (await waveformToggle.isVisible()) {
      await waveformToggle.click();
    }

    // Wait for waveforms to load
    await page.waitForTimeout(2000);

    // Check for waveform containers
    const waveformContainers = page
      .locator(".bg-black\\/30")
      .filter({ hasText: "" });
    if (await waveformContainers.first().isVisible()) {
      await expect(waveformContainers.first()).toBeVisible();
    }

    // Check for waveform visualization header
    const waveformHeader = page.locator("text=Waveform Visualization");
    if (await waveformHeader.isVisible()) {
      await expect(waveformHeader).toBeVisible();
    }
  });

  test("should handle audio playback controls", async ({ page }) => {
    // Wait for page to load completely
    await page.waitForLoadState("networkidle");

    // Wait for audio to finish loading
    await page.waitForFunction(
      () => {
        const loadingTexts = document.querySelectorAll("*");
        for (const el of loadingTexts) {
          if (el.textContent?.includes("Loading audio...")) {
            return false;
          }
        }
        return true;
      },
      {},
      { timeout: 10000 }
    );

    // Find audio comparison components
    const audioComparisons = page
      .locator('[data-testid="audio-comparison"]')
      .or(page.locator(".space-y-6 > div").filter({ hasText: "Unmixed" }));

    if (await audioComparisons.first().isVisible()) {
      const firstComparison = audioComparisons.first();

      // Check for playback controls
      const playButton = firstComparison
        .locator("button")
        .filter({ hasText: /Play|Pause/ })
        .first();
      const resetButton = firstComparison
        .locator("button")
        .filter({ hasText: /Reset/ })
        .first();
      const muteButton = firstComparison
        .locator("button")
        .filter({ hasText: /Mute|Unmute/ })
        .first();

      if (await playButton.isVisible()) {
        await expect(playButton).toBeEnabled();

        // Test play button functionality
        await playButton.click();
        // Note: We can't easily test actual audio playback in Playwright,
        // but we can verify the button state changes
        await page.waitForTimeout(500);
      }

      if (await resetButton.isVisible()) {
        await expect(resetButton).toBeEnabled();
      }

      if (await muteButton.isVisible()) {
        await expect(muteButton).toBeEnabled();
      }
    }
  });

  test("should display audio metadata correctly", async ({ page }) => {
    // Wait for page to load
    await page.waitForLoadState("networkidle");

    // Check for audio comparison titles and metadata
    const comparisonTitles = page.locator("h3").filter({ hasText: /.+/ });
    if (await comparisonTitles.first().isVisible()) {
      await expect(comparisonTitles.first()).toBeVisible();
    }

    // Check for artist information
    const artistInfo = page
      .locator("text=by ")
      .or(page.locator(".text-gray-400"));
    if (await artistInfo.first().isVisible()) {
      await expect(artistInfo.first()).toBeVisible();
    }

    // Check for time displays
    const timeDisplays = page.locator("text=/\\d+:\\d+/");
    if (await timeDisplays.first().isVisible()) {
      await expect(timeDisplays.first()).toBeVisible();
    }
  });
});

test.describe("Audio Data Synchronization", () => {
  test("should sync data between admin and portfolio pages", async ({
    page,
    context,
  }) => {
    // Open admin page in a new tab
    const adminPage = await context.newPage();
    await adminPage.goto("/admin/audio");

    // Navigate to portfolio page in original tab
    await page.goto("/portfolio");
    await page.waitForLoadState("networkidle");

    // Note: In a real test environment, you would:
    // 1. Create a new audio comparison in admin
    // 2. Refresh portfolio page
    // 3. Verify the new comparison appears
    // 4. Delete the comparison in admin
    // 5. Refresh portfolio page
    // 6. Verify the comparison is removed

    // For now, just verify that portfolio page loads data dynamically
    await page.reload();
    await page.waitForLoadState("networkidle");

    // Wait for any loading states to complete
    await page.waitForFunction(
      () => {
        const loadingTexts = document.querySelectorAll("*");
        for (const el of loadingTexts) {
          if (el.textContent?.includes("Loading audio...")) {
            return false;
          }
        }
        return true;
      },
      {},
      { timeout: 10000 }
    );

    // Verify comparisons are still displayed after reload
    const reloadedComparisons = await page
      .locator(".space-y-6 > div")
      .filter({ hasText: "Unmixed" })
      .count();

    // The count should be consistent (data is loaded from API, not static)
    expect(reloadedComparisons).toBeGreaterThanOrEqual(0);

    await adminPage.close();
  });

  test("should handle empty state when no audio comparisons exist", async ({
    page,
  }) => {
    await page.goto("/portfolio");
    await page.waitForLoadState("networkidle");

    // Wait for loading to complete
    await page.waitForFunction(
      () => {
        const loadingTexts = document.querySelectorAll("*");
        for (const el of loadingTexts) {
          if (el.textContent?.includes("Loading audio...")) {
            return false;
          }
        }
        return true;
      },
      {},
      { timeout: 10000 }
    );

    // Check if there are any audio comparisons
    const mixingComparisons = page
      .locator("h2", { hasText: "Mixing Comparisons" })
      .locator("..")
      .locator(".space-y-6 > div");
    const masteringComparisons = page
      .locator("h2", { hasText: "Mastering Comparisons" })
      .locator("..")
      .locator(".space-y-6 > div");

    const mixingCount = await mixingComparisons.count();
    const masteringCount = await masteringComparisons.count();

    // If no comparisons exist, verify appropriate empty state messages
    if (mixingCount === 0) {
      const emptyMessage = page.locator(
        "text=No mixing comparisons available yet"
      );
      if (await emptyMessage.isVisible()) {
        await expect(emptyMessage).toBeVisible();
      }
    }

    if (masteringCount === 0) {
      const emptyMessage = page.locator(
        "text=No mastering comparisons available yet"
      );
      if (await emptyMessage.isVisible()) {
        await expect(emptyMessage).toBeVisible();
      }
    }
  });

  test("should load audio comparisons from API", async ({ page }) => {
    // Intercept API calls to verify data is loaded dynamically
    let apiCalled = false;
    await page.route("/api/audio/comparisons", (route) => {
      apiCalled = true;
      route.continue();
    });

    await page.goto("/portfolio");
    await page.waitForLoadState("networkidle");

    // Wait a bit for the API call to happen
    await page.waitForTimeout(2000);

    // Verify that the API was called (indicating dynamic loading)
    expect(apiCalled).toBe(true);
  });

  test("should handle audio loading errors gracefully", async ({ page }) => {
    // Navigate to portfolio page
    await page.goto("/portfolio");
    await page.waitForLoadState("networkidle");

    // Wait for any loading states to complete
    await page.waitForFunction(
      () => {
        const loadingTexts = document.querySelectorAll("*");
        for (const el of loadingTexts) {
          if (el.textContent?.includes("Loading audio...")) {
            return false;
          }
        }
        return true;
      },
      {},
      { timeout: 15000 }
    );

    // Check for any error messages in console (logged by our error handling)
    const logs = [];
    page.on("console", (msg) => {
      if (msg.type() === "error") {
        logs.push(msg.text());
      }
    });

    // Refresh page to potentially trigger loading errors
    await page.reload();
    await page.waitForLoadState("networkidle");

    // Wait for loading to complete again
    await page.waitForFunction(
      () => {
        const loadingTexts = document.querySelectorAll("*");
        for (let el of loadingTexts) {
          if (el.textContent?.includes("Loading audio...")) {
            return false;
          }
        }
        return true;
      },
      {},
      { timeout: 15000 }
    );

    // Verify page still functions even if there were loading errors
    await expect(page.locator("h1")).toContainText("Our Portfolio");
    await expect(page.getByRole("tablist")).toBeVisible();

    // If there are audio comparisons, verify they're still interactive
    const audioComparisons = page
      .locator(".space-y-6 > div")
      .filter({ hasText: "Unmixed" });

    if ((await audioComparisons.count()) > 0) {
      const firstComparison = audioComparisons.first();
      const playButton = firstComparison
        .locator("button")
        .filter({ hasText: /Play|Pause/ })
        .first();

      if (await playButton.isVisible()) {
        // Button should still be enabled even if audio failed to load
        await expect(playButton).toBeEnabled();
      }
    }
  });
});

test.describe("Admin Audio Upload System", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the admin audio upload page
    await page.goto("/admin/audio");
  });

  test("should display audio upload interface", async ({ page }) => {
    // Check page title and description - use more specific selector
    await expect(
      page.locator("h1").filter({ hasText: "Audio Comparison Manager" })
    ).toBeVisible();
    await expect(
      page.locator("text=Manage audio comparisons using Vercel Blob URLs")
    ).toBeVisible();

    // Check main sections
    await expect(page.locator("text=Add New Comparison")).toBeVisible();
    await expect(page.locator("text=Existing Audio Comparisons")).toBeVisible();
  });

  test("should have all required form fields", async ({ page }) => {
    // Open the add new comparison dialog
    await page.click("text=Add New Comparison");

    // Check metadata form fields
    await expect(page.locator("label", { hasText: "Title" })).toBeVisible();
    await expect(page.locator("label", { hasText: "Artist" })).toBeVisible();
    await expect(page.locator("label", { hasText: "Category" })).toBeVisible();
    await expect(
      page.locator("label", { hasText: "Project ID" })
    ).toBeVisible();
    await expect(
      page.locator("label", { hasText: "Tags (comma-separated)" })
    ).toBeVisible();

    // Check input fields are functional
    await page.fill('input[placeholder="Song title"]', "Test Song");
    await page.fill('input[placeholder="Artist name"]', "Test Artist");
    await page.fill(
      'input[placeholder="unique-project-id"]',
      "test-project-123"
    );
    await page.fill('input[placeholder*="hip-hop"]', "test, demo, sample");

    // Verify values were entered
    await expect(page.locator('input[placeholder="Song title"]')).toHaveValue(
      "Test Song"
    );
    await expect(page.locator('input[placeholder="Artist name"]')).toHaveValue(
      "Test Artist"
    );
  });

  test("should have working dropdown selectors", async ({ page }) => {
    // Open the add new comparison dialog first
    await page.click("text=Add New Comparison");

    // Test Category selector
    await page.click('[role="combobox"]', { timeout: 5000 });

    // Check that dropdown options are available (use more specific selectors)
    await expect(
      page.getByRole("option", { name: "Mixing Comparison" })
    ).toBeVisible();
    await expect(
      page.getByRole("option", { name: "Mastering Comparison" })
    ).toBeVisible();

    // Select an option
    await page.getByRole("option", { name: "Mastering Comparison" }).click();
  });

  test("should display URL input fields", async ({ page }) => {
    // Open the add new comparison dialog first
    await page.click("text=Add New Comparison");

    // Check URL input fields are present
    await expect(
      page.locator('input[placeholder="https://your-blob-url.com/audio.mp3"]')
    ).toHaveCount(2); // Should have 2 URL inputs (before and after)

    // Check that URL labels are dynamic based on category
    await expect(page.locator("text=Audio URL")).toHaveCount(2);
  });
});

test.describe("Admin File Management System", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/admin/manage");
  });

  test("should display file management interface", async ({ page }) => {
    // Check page elements - use more specific selector
    await expect(
      page.locator("h1").filter({ hasText: "Manage Audio Files" })
    ).toBeVisible();
    await expect(
      page.locator("text=View, search, and manage your uploaded audio files")
    ).toBeVisible();

    // Check search functionality
    await expect(
      page.locator('input[placeholder="Search files..."]')
    ).toBeVisible();
    await expect(page.getByRole("button", { name: /Refresh/i })).toBeVisible();

    // Check statistics cards
    await expect(page.locator("text=Total Files")).toBeVisible();
    await expect(page.locator("text=Total Size")).toBeVisible();
    await expect(page.locator("text=Mixing Files")).toBeVisible();
  });

  test("should handle empty state gracefully", async ({ page }) => {
    // If no files are uploaded, should show appropriate message
    const noFilesMessage = page.locator("text=No audio files uploaded yet");
    const filesTable = page.locator("table");

    // Either show empty state or files table
    const hasFiles = await filesTable.isVisible();
    if (!hasFiles) {
      await expect(noFilesMessage).toBeVisible();
    }
  });

  test("should have functional search", async ({ page }) => {
    const searchInput = page.locator('input[placeholder="Search files..."]');

    // Test search functionality
    await searchInput.fill("test");
    await expect(searchInput).toHaveValue("test");

    // Clear search
    await searchInput.clear();
    await expect(searchInput).toHaveValue("");
  });
});

test.describe("Admin Navigation", () => {
  test("should navigate between admin pages", async ({ page }) => {
    // Test admin dashboard
    await page.goto("/admin");
    await expect(
      page.locator("h1").filter({ hasText: "Admin Dashboard" })
    ).toBeVisible();

    // Test audio upload page
    await page.goto("/admin/audio");
    await expect(
      page.locator("h1").filter({ hasText: "Audio Comparison Manager" })
    ).toBeVisible();

    // Test file management page
    await page.goto("/admin/manage");
    await expect(
      page.locator("h1").filter({ hasText: "Manage Audio Files" })
    ).toBeVisible();

    // Test navigation back to main site
    await page.goto("/");
    await expect(page.locator("h1")).toBeVisible(); // Main site should have an h1
  });

  test("should have consistent admin layout", async ({ page }) => {
    await page.goto("/admin");

    // Check admin navigation elements
    await expect(page.locator("text=Admin Panel")).toBeVisible();
    await expect(
      page.getByRole("button", { name: /Back to Site/i })
    ).toBeVisible();
    await expect(
      page.getByRole("button", { name: /Audio Upload/i })
    ).toBeVisible();
    await expect(
      page.getByRole("button", { name: "Manage Files", exact: true }).first()
    ).toBeVisible();
  });
});

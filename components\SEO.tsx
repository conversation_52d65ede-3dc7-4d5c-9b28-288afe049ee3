"use client";

import Head from "next/head";
import {
  getSEOConfig,
  getCanonicalUrl,
  getOrganizationStructuredData,
  siteConfig,
} from "@/lib/seo-config";

interface SEOProps {
  page: string;
  customTitle?: string;
  customDescription?: string;
  customKeywords?: string[];
  customCanonical?: string;
  customImage?: string;
  structuredData?: object;
  noIndex?: boolean;
}

export function SEO({
  page,
  customTitle,
  customDescription,
  customKeywords,
  customCanonical,
  customImage,
  structuredData,
  noIndex = false,
}: SEOProps) {
  const config = getSEOConfig(page);

  // Use custom values if provided, otherwise fall back to config
  const title = customTitle || config.title;
  const description = customDescription || config.description;
  const keywords = customKeywords || config.keywords || [];
  const canonical = customCanonical || getCanonicalUrl(config.canonical || "/");
  const ogImage =
    customImage || config.openGraph?.image || `${siteConfig.url}/NC Orange.png`;

  // Combine organization structured data with any custom structured data
  const combinedStructuredData = [
    getOrganizationStructuredData(),
    ...(structuredData ? [structuredData] : []),
  ];

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords.length > 0 && (
        <meta name="keywords" content={keywords.join(", ")} />
      )}

      {/* Canonical URL */}
      <link rel="canonical" href={canonical} />

      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}

      {/* Open Graph */}
      <meta property="og:title" content={config.openGraph?.title || title} />
      <meta
        property="og:description"
        content={config.openGraph?.description || description}
      />
      <meta property="og:type" content={config.openGraph?.type || "website"} />
      <meta property="og:url" content={canonical} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:site_name" content={siteConfig.name} />

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />

      {/* Additional Meta Tags */}
      <meta name="author" content={siteConfig.name} />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta httpEquiv="Content-Language" content="en" />

      {/* Structured Data */}
      {combinedStructuredData.map((data, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(data),
          }}
        />
      ))}
    </Head>
  );
}

import { NextResponse } from "next/server";
import { z } from "zod";
import sgMail from "@sendgrid/mail";

// Set SendGrid API key
// In production, this should be set as an environment variable
sgMail.setApiKey(process.env.SENDGRID_API_KEY || "");

// Define form schema for validation
const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  subject: z
    .string()
    .min(5, { message: "Subject must be at least 5 characters" }),
  message: z
    .string()
    .min(10, { message: "Message must be at least 10 characters" }),
  projectType: z.string().optional(),
  budget: z.string().optional(),
  timeline: z.string().optional(),
});

export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json();

    // Validate form data
    const result = formSchema.safeParse(body);

    if (!result.success) {
      // Return validation errors
      return NextResponse.json(
        {
          success: false,
          errors: result.error.format(),
        },
        { status: 400 }
      );
    }

    const { name, email, subject, message, projectType, budget, timeline } =
      result.data;

    // Prepare email content
    const emailContent = `
      Name: ${name}
      Email: ${email}
      Subject: ${subject}
      ${projectType ? `Project Type: ${projectType}` : ""}
      ${budget ? `Budget: ${budget}` : ""}
      ${timeline ? `Timeline: ${timeline}` : ""}
      
      Message:
      ${message}
    `;

    // Configure email
    const msg = {
      to: process.env.RECIPIENT_EMAIL || "<EMAIL>", // Fallback email
      from: process.env.SENDER_EMAIL || "<EMAIL>", // Must be verified in SendGrid
      subject: `New Contact Form Submission: ${subject}`,
      text: emailContent,
      html: emailContent.replace(/\n/g, "<br>"),
    };

    // Send email
    await sgMail.send(msg);

    // Return success response
    return NextResponse.json({
      success: true,
      message: "Your message has been sent successfully!",
    });
  } catch (error) {
    console.error("Error sending email:", error);

    // Improved error handling with proper typing
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    console.error("Error details:", error);
    
    // Check for specific SendGrid errors
    if (error instanceof Error && 'code' in error && error.code === 403) {
      return NextResponse.json(
        {
          success: false,
          message: "Email service authentication failed. Please contact support.",
        },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        message: "Failed to send message. Please try again later.",
        debug: process.env.NODE_ENV === "development" ? errorMessage : undefined,
      },
      { status: 500 }
    );
  }
}

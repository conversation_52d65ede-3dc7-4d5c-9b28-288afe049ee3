import { Metadata } from "next";
import { getSEOConfig, getCanonicalUrl } from "@/lib/seo-config";
import AboutClient from "./about-client";

export const metadata: Metadata = (() => {
  const config = getSEOConfig("about");
  return {
    title: config.title,
    description: config.description,
    keywords: config.keywords,
    alternates: {
      canonical: getCanonicalUrl(config.canonical || "/about"),
    },
    openGraph: {
      title: config.openGraph?.title || config.title,
      description: config.openGraph?.description || config.description,
      type: config.openGraph?.type || "website",
      images: config.openGraph?.image ? [config.openGraph.image] : undefined,
    },
    twitter: {
      card: "summary_large_image",
      title: config.title,
      description: config.description,
      images: config.openGraph?.image ? [config.openGraph.image] : undefined,
    },
  };
})();

export default function AboutPage() {
  return <AboutClient />;
}

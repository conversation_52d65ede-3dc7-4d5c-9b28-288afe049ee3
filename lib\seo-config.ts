/**
 * SEO Configuration for Noize Capital
 * Based on comprehensive SEO recommendations document
 */

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  canonical?: string;
  openGraph?: {
    title?: string;
    description?: string;
    type?:
      | "website"
      | "article"
      | "profile"
      | "book"
      | "music.song"
      | "music.album"
      | "music.playlist"
      | "music.radio_station"
      | "video.movie"
      | "video.episode"
      | "video.tv_show"
      | "video.other";
    image?: string;
  };
}

export const siteConfig = {
  name: "Noize Capital",
  url: "https://noizecapital.com",
  description:
    "Professional audio production company specializing in mixing, mastering, and full production services for independent musicians.",
  keywords: [
    "mixing",
    "mastering",
    "music production",
    "audio production",
    "independent artists",
    "hip-hop",
    "r&b",
    "afro-tech",
    "professional audio services",
  ],
};

export const seoConfig: Record<string, SEOConfig> = {
  // Homepage
  home: {
    title: "Professional Mixing & Mastering Services | Noize Capital",
    description:
      "Elevate your music with professional mixing and mastering services from Noize Capital. Precision sound for independent artists across Hip-Hop, R&B, and Afro-Tech. Book your session today.",
    keywords: [
      "professional mixing services",
      "mastering services",
      "music production",
      "independent artists",
      "hip-hop mixing",
      "r&b mastering",
      "afro-tech production",
      "audio engineering",
    ],
    canonical: "/",
    openGraph: {
      title: "Professional Mixing & Mastering Services | Noize Capital",
      description:
        "Elevate your music with professional mixing and mastering services from Noize Capital. Precision sound for independent artists across Hip-Hop, R&B, and Afro-Tech.",
      type: "website",
      image: "/NC Orange.png",
    },
  },

  // Services Page
  services: {
    title: "Mixing, Mastering & Production Services | Noize Capital",
    description:
      "Professional audio services including mixing ($35), mastering ($20), and full production packages ($50). Quality sound for independent artists with fast turnaround times.",
    keywords: [
      "mixing services",
      "mastering services",
      "music production services",
      "audio mixing",
      "professional mastering",
      "full production packages",
      "independent artist services",
    ],
    canonical: "/services",
    openGraph: {
      title: "Professional Audio Services | Noize Capital",
      description:
        "Professional audio services including mixing, mastering, and full production packages. Quality sound for independent artists.",
      type: "website",
    },
  },

  // Portfolio Page
  portfolio: {
    title:
      "Music Production Portfolio | Hip-Hop, R&B & Afro-Tech | Noize Capital",
    description:
      "Explore our portfolio of professional mixing, mastering, and production work across Hip-Hop, R&B, and Afro-Tech genres. Listen to samples and see how we can elevate your sound.",
    keywords: [
      "music production portfolio",
      "hip-hop mixing examples",
      "r&b mastering samples",
      "afro-tech production",
      "professional audio portfolio",
      "mixing samples",
      "mastering examples",
    ],
    canonical: "/portfolio",
    openGraph: {
      title: "Music Production Portfolio | Noize Capital",
      description:
        "Explore our portfolio of professional mixing, mastering, and production work across Hip-Hop, R&B, and Afro-Tech genres.",
      type: "website",
    },
  },

  // About Page
  about: {
    title: "About Noize Capital | Professional Audio Production Team",
    description:
      "Meet the team behind Noize Capital. Founded in 2020 by Melvin Mpolokeng, we've grown from a bedroom studio to a full-service audio production company serving independent artists.",
    keywords: [
      "noize capital team",
      "melvin mpolokeng",
      "audio production company",
      "professional audio engineers",
      "music production team",
      "independent artist services",
    ],
    canonical: "/about",
    openGraph: {
      title: "About Noize Capital | Professional Audio Production Team",
      description:
        "Meet the team behind Noize Capital. Founded in 2020 by Melvin Mpolokeng, we've grown to serve independent artists worldwide.",
      type: "website",
    },
  },

  // Contact Page
  contact: {
    title: "Contact Noize Capital | Book Your Mixing & Mastering Session",
    description:
      "Ready to elevate your sound? Contact Noize Capital to discuss your project requirements and book professional mixing, mastering, or full production services.",
    keywords: [
      "contact noize capital",
      "book mixing session",
      "book mastering session",
      "music production booking",
      "professional audio services contact",
      "get quote mixing mastering",
    ],
    canonical: "/contact",
    openGraph: {
      title: "Contact Noize Capital | Book Your Session",
      description:
        "Ready to elevate your sound? Contact us to discuss your project and book professional audio services.",
      type: "website",
    },
  },

  // Blog Page
  blog: {
    title: "Music Production Tips & Tutorials | Noize Capital Blog",
    description:
      "Expert advice on mixing, mastering, and music production from professional audio engineers. Learn techniques to improve your sound and advance your music career.",
    keywords: [
      "music production tips",
      "mixing tutorials",
      "mastering guides",
      "audio engineering advice",
      "music production blog",
      "professional mixing tips",
      "mastering techniques",
    ],
    canonical: "/blog",
    openGraph: {
      title: "Music Production Tips & Tutorials | Noize Capital Blog",
      description:
        "Expert advice on mixing, mastering, and music production from professional audio engineers.",
      type: "website",
    },
  },
};

/**
 * Get SEO configuration for a specific page
 */
export function getSEOConfig(page: string): SEOConfig {
  return seoConfig[page] || seoConfig.home;
}

/**
 * Generate canonical URL for a page
 */
export function getCanonicalUrl(path: string): string {
  return `${siteConfig.url}${path}`;
}

/**
 * Generate structured data for organization
 */
export function getOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    logo: `${siteConfig.url}/NC Orange.png`,
    sameAs: [
      // Add social media URLs when available
    ],
    contactPoint: {
      "@type": "ContactPoint",
      contactType: "customer service",
      url: `${siteConfig.url}/contact`,
    },
    areaServed: "Worldwide",
    serviceType: [
      "Audio Mixing",
      "Audio Mastering",
      "Music Production",
      "Audio Engineering",
    ],
  };
}

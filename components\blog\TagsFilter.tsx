"use client";

import { Badge } from "@/components/ui/badge";
import { ScrollA<PERSON>, ScrollBar } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";

interface TagsFilterProps {
  tags: string[];
  selectedTag?: string;
}

export function TagsFilter({ tags, selectedTag }: TagsFilterProps) {
  const router = useRouter();

  const handleTagSelect = (tag: string | null) => {
    const url = new URL(window.location.href);
    if (tag) {
      url.searchParams.set("tag", tag);
    } else {
      url.searchParams.delete("tag");
    }
    router.push(url.pathname + url.search);
  };
  return (
    <ScrollArea className="w-full whitespace-nowrap rounded-lg border">
      <div className="flex space-x-2 sm:space-x-3 p-3 sm:p-4">
        <Badge
          variant={!selectedTag ? "default" : "outline"}
          className={cn(
            "cursor-pointer hover:bg-primary/80 px-3 py-1.5 text-sm sm:text-base",
            !selectedTag && "bg-primary"
          )}
          onClick={() => handleTagSelect(null)}
        >
          All Posts
        </Badge>
        {tags.map((tag) => (
          <Badge
            key={tag}
            variant={selectedTag === tag ? "default" : "outline"}
            className={cn(
              "cursor-pointer hover:bg-primary/80 px-3 py-1.5 text-sm sm:text-base",
              selectedTag === tag && "bg-primary",
              "transition-all duration-200 hover:scale-105"
            )}
            onClick={() => handleTagSelect(tag)}
          >
            {tag}
          </Badge>
        ))}
      </div>
      <ScrollBar orientation="horizontal" className="h-2.5 sm:h-2" />
    </ScrollArea>
  );
}

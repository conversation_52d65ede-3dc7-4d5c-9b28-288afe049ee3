/**
 * Audio URL validation utilities
 */

export interface AudioValidationResult {
  isValid: boolean;
  error?: string;
  duration?: number;
  format?: string;
}

/**
 * Validate if a URL points to a valid audio file
 */
export async function validateAudioUrl(
  url: string
): Promise<AudioValidationResult> {
  if (!url || !url.trim()) {
    return { isValid: false, error: "URL is required" };
  }

  // Basic URL format validation
  try {
    new URL(url);
  } catch {
    return { isValid: false, error: "Invalid URL format" };
  }

  // Check if URL looks like an audio file
  const audioExtensions = [".mp3", ".wav", ".m4a", ".aac", ".ogg", ".flac"];
  const hasAudioExtension = audioExtensions.some((ext) =>
    url.toLowerCase().includes(ext)
  );

  if (!hasAudioExtension && !url.includes("blob.vercel-storage.com")) {
    return {
      isValid: false,
      error: "URL does not appear to be an audio file",
    };
  }

  // Test if the audio can be loaded
  return new Promise((resolve) => {
    const audio = new Audio();
    const timeout = setTimeout(() => {
      resolve({
        isValid: false,
        error: "Audio loading timeout (5 seconds)",
      });
    }, 5000);

    audio.oncanplaythrough = () => {
      clearTimeout(timeout);
      resolve({
        isValid: true,
        duration: audio.duration,
        format: getAudioFormat(url),
      });
    };

    audio.onerror = () => {
      clearTimeout(timeout);
      resolve({
        isValid: false,
        error: "Failed to load audio file",
      });
    };

    audio.onloadedmetadata = () => {
      // If we can load metadata but not play through, still consider it valid
      if (audio.duration > 0) {
        clearTimeout(timeout);
        resolve({
          isValid: true,
          duration: audio.duration,
          format: getAudioFormat(url),
        });
      }
    };

    try {
      audio.src = url;
    } catch {
      clearTimeout(timeout);
      resolve({
        isValid: false,
        error: "Failed to set audio source",
      });
    }
  });
}

/**
 * Get audio format from URL
 */
function getAudioFormat(url: string): string {
  const audioFormats: { [key: string]: string } = {
    ".mp3": "MP3",
    ".wav": "WAV",
    ".m4a": "M4A",
    ".aac": "AAC",
    ".ogg": "OGG",
    ".flac": "FLAC",
  };

  for (const [extension, format] of Object.entries(audioFormats)) {
    if (url.toLowerCase().includes(extension)) {
      return format;
    }
  }

  return "Unknown";
}

/**
 * Format duration in seconds to MM:SS format
 */
export function formatDuration(seconds: number): string {
  if (!seconds || !isFinite(seconds)) return "0:00";

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}

/**
 * Check if URL is a Vercel Blob URL
 */
export function isVercelBlobUrl(url: string): boolean {
  return url.includes("blob.vercel-storage.com");
}

/**
 * Validate multiple audio URLs concurrently
 */
export async function validateMultipleAudioUrls(
  urls: string[]
): Promise<AudioValidationResult[]> {
  const validationPromises = urls.map((url) => validateAudioUrl(url));
  return Promise.all(validationPromises);
}

// Global audio manager to ensure only one audio plays at a time
class GlobalAudioManager {
  private currentAudio: HTMLAudioElement | null = null;
  private currentPreview: AudioPreview | null = null;

  setCurrentAudio(audio: HTMLAudioElement, preview: AudioPreview) {
    // Stop any currently playing audio
    if (this.currentAudio && !this.currentAudio.paused) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
    }

    this.currentAudio = audio;
    this.currentPreview = preview;
  }

  stopCurrent() {
    if (this.currentAudio && !this.currentAudio.paused) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
    }
    this.currentAudio = null;
    this.currentPreview = null;
  }

  getCurrentPreview() {
    return this.currentPreview;
  }

  isCurrentPreview(preview: AudioPreview) {
    return this.currentPreview === preview;
  }
}

const globalAudioManager = new GlobalAudioManager();

/**
 * Preview audio by creating a temporary audio element
 */
export class AudioPreview {
  private audio: HTMLAudioElement | null = null;
  private onTimeUpdate?: (currentTime: number, duration: number) => void;
  private onEnded?: () => void;
  private onPlayStateChange?: (isPlaying: boolean) => void;

  constructor(
    onTimeUpdate?: (currentTime: number, duration: number) => void,
    onEnded?: () => void,
    onPlayStateChange?: (isPlaying: boolean) => void
  ) {
    this.onTimeUpdate = onTimeUpdate;
    this.onEnded = onEnded;
    this.onPlayStateChange = onPlayStateChange;
  }

  async load(url: string): Promise<boolean> {
    this.cleanup();

    this.audio = new Audio();
    this.audio.src = url;

    if (this.onTimeUpdate) {
      this.audio.ontimeupdate = () => {
        if (this.audio && this.onTimeUpdate) {
          this.onTimeUpdate(this.audio.currentTime, this.audio.duration);
        }
      };
    }

    this.audio.onended = () => {
      globalAudioManager.stopCurrent();
      this.onPlayStateChange?.(false);
      this.onEnded?.();
    };

    this.audio.onplay = () => {
      globalAudioManager.setCurrentAudio(this.audio!, this);
      this.onPlayStateChange?.(true);
    };

    this.audio.onpause = () => {
      this.onPlayStateChange?.(false);
    };

    return new Promise((resolve) => {
      if (!this.audio) {
        resolve(false);
        return;
      }

      this.audio.oncanplaythrough = () => resolve(true);
      this.audio.onerror = () => resolve(false);
    });
  }

  async play(): Promise<boolean> {
    if (!this.audio) return false;

    // Stop any other currently playing audio
    globalAudioManager.stopCurrent();

    try {
      await this.audio.play();
      return true;
    } catch {
      return false;
    }
  }

  pause(): void {
    if (this.audio) {
      this.audio.pause();
    }
  }

  stop(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
      if (globalAudioManager.isCurrentPreview(this)) {
        globalAudioManager.stopCurrent();
      }
    }
  }

  /**
   * Toggle play/pause state
   */
  async toggle(): Promise<boolean> {
    if (!this.audio) return false;

    if (this.isPlaying) {
      this.pause();
      return false;
    } else {
      return await this.play();
    }
  }

  seek(time: number): void {
    if (this.audio) {
      this.audio.currentTime = time;
    }
  }

  setVolume(volume: number): void {
    if (this.audio) {
      this.audio.volume = Math.max(0, Math.min(1, volume));
    }
  }

  get isPlaying(): boolean {
    return this.audio ? !this.audio.paused : false;
  }

  get currentTime(): number {
    return this.audio?.currentTime || 0;
  }

  get duration(): number {
    return this.audio?.duration || 0;
  }

  cleanup(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.src = "";
      if (globalAudioManager.isCurrentPreview(this)) {
        globalAudioManager.stopCurrent();
      }
      this.audio = null;
    }
  }
}

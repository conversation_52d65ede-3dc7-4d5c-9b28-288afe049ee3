import { AudioComparison } from "./audio-storage";

// Sample audio comparisons data
// In production, this would come from a database or CMS
export const audioComparisons: AudioComparison[] = [
  {
    id: "mixing-comparison-1",
    title: "Half Past, Jampas",
    artist: "T-Status feat. Stat",
    category: "mixing-comparison",
    beforeAudio: {
      id: "half-past-unmixed",
      title: "Half Past, Jampas (Unmixed)",
      artist: "T-Status feat. Stat",
      type: "unmixed",
      category: "mixing-comparison",
      projectId: "half-past-jampas",
      url: "https://jcimm7djdqsggcaf.public.blob.vercel-storage.com/Half%20pass%20%28Jumpas%29_UNMIXED.mp3", // Replace with actual Vercel Blob URLs
      uploadedAt: "2025-01-01T00:00:00Z",
      tags: ["hip-hop", "collaboration"],
    },
    afterAudio: {
      id: "half-past-mixed",
      title: "Half Past, Jampas (Mixed)",
      artist: "T-Status feat. Stat",
      type: "mixed",
      category: "mixing-comparison",
      projectId: "half-past-jampas",
      url: "https://jcimm7djdqsggcaf.public.blob.vercel-storage.com/Half%20pass%20%28Jumpas%29_MIXED.mp3", // Replace with actual Vercel Blob URLs
      uploadedAt: "2025-01-01T00:00:00Z",
      tags: ["hip-hop", "collaboration"],
    },
    description:
      "Professional mixing transformation showcasing balanced levels, spatial positioning, and dynamic processing.",
    coverImage: "/project1.jpg",
    year: 2025,
    tags: ["hip-hop", "collaboration", "vocal-heavy"],
  },
  {
    id: "mastering-comparison-1",
    title: "Thixo",
    artist: "T-Status",
    category: "mastering-comparison",
    beforeAudio: {
      id: "thixo-unmastered",
      title: "Thixo (Unmastered)",
      artist: "T-Status",
      type: "unmastered",
      category: "mastering-comparison",
      projectId: "thixo",
      url: "https://example.com/audio/thixo-unmastered.mp3", // Replace with actual Vercel Blob URLs
      uploadedAt: "2025-01-01T00:00:00Z",
      tags: ["afrobeat", "solo"],
    },
    afterAudio: {
      id: "thixo-mastered",
      title: "Thixo (Mastered)",
      artist: "T-Status",
      type: "mastered",
      category: "mastering-comparison",
      projectId: "thixo",
      url: "https://example.com/audio/thixo-mastered.mp3", // Replace with actual Vercel Blob URLs
      uploadedAt: "2025-01-01T00:00:00Z",
      tags: ["afrobeat", "solo"],
    },
    description:
      "Mastering process demonstrating loudness optimization, frequency enhancement, and stereo imaging.",
    coverImage: "/project2.jpg",
    year: 2025,
    tags: ["afrobeat", "solo", "energetic"],
  },
  {
    id: "mixing-comparison-2",
    title: "Umdeni",
    artist: "T-Status",
    category: "mixing-comparison",
    beforeAudio: {
      id: "umdeni-unmixed",
      title: "Umdeni (Unmixed)",
      artist: "T-Status",
      type: "unmixed",
      category: "mixing-comparison",
      projectId: "umdeni",
      url: "https://example.com/audio/umdeni-unmixed.mp3", // Replace with actual Vercel Blob URLs
      uploadedAt: "2023-01-01T00:00:00Z",
      tags: ["traditional", "cultural"],
    },
    afterAudio: {
      id: "umdeni-mixed",
      title: "Umdeni (Mixed)",
      artist: "T-Status",
      type: "mixed",
      category: "mixing-comparison",
      projectId: "umdeni",
      url: "https://example.com/audio/umdeni-mixed.mp3", // Replace with actual Vercel Blob URLs
      uploadedAt: "2023-01-01T00:00:00Z",
      tags: ["traditional", "cultural"],
    },
    description:
      "Mixing approach that preserves cultural authenticity while enhancing modern production values.",
    coverImage: "/project3.jpg",
    year: 2023,
    tags: ["traditional", "cultural", "authentic"],
  },
  {
    id: "mastering-comparison-2",
    title: "Amadoda",
    artist: "KilloTronix feat. Nkosana Mkhonza & Vuvu The Drummer",
    category: "mastering-comparison",
    beforeAudio: {
      id: "amadoda-unmastered",
      title: "Amadoda (Unmastered)",
      artist: "KilloTronix feat. Nkosana Mkhonza & Vuvu The Drummer",
      type: "unmastered",
      category: "mastering-comparison",
      projectId: "amadoda",
      url: "https://example.com/audio/amadoda-unmastered.mp3", // Replace with actual Vercel Blob URLs
      uploadedAt: "2023-01-01T00:00:00Z",
      tags: ["electronic", "collaboration", "drums"],
    },
    afterAudio: {
      id: "amadoda-mastered",
      title: "Amadoda (Mastered)",
      artist: "KilloTronix feat. Nkosana Mkhonza & Vuvu The Drummer",
      type: "mastered",
      category: "mastering-comparison",
      projectId: "amadoda",
      url: "https://example.com/audio/amadoda-mastered.mp3", // Replace with actual Vercel Blob URLs
      uploadedAt: "2023-01-01T00:00:00Z",
      tags: ["electronic", "collaboration", "drums"],
    },
    description:
      "Complex mastering project balancing electronic elements with live drums and vocals.",
    coverImage: "/project4.jpg",
    year: 2023,
    tags: ["electronic", "collaboration", "drums", "complex"],
  },
];

// Helper functions for filtering and organizing audio comparisons
export function getAudioComparisonsByCategory(
  category: "mixing-comparison" | "mastering-comparison"
): AudioComparison[] {
  return audioComparisons.filter(
    (comparison) => comparison.category === category
  );
}

export function getAudioComparisonById(
  id: string
): AudioComparison | undefined {
  return audioComparisons.find((comparison) => comparison.id === id);
}

export function getAudioComparisonsByYear(year: number): AudioComparison[] {
  return audioComparisons.filter((comparison) => comparison.year === year);
}

export function getAudioComparisonsByTag(tag: string): AudioComparison[] {
  return audioComparisons.filter((comparison) => comparison.tags.includes(tag));
}

export function getAllTags(): string[] {
  const tags = new Set<string>();
  audioComparisons.forEach((comparison) => {
    comparison.tags.forEach((tag) => tags.add(tag));
  });
  return Array.from(tags).sort();
}

export function getAllYears(): number[] {
  const years = new Set<number>();
  audioComparisons.forEach((comparison) => {
    years.add(comparison.year);
  });
  return Array.from(years).sort((a, b) => b - a); // Most recent first
}

// Statistics
export function getAudioComparisonStats() {
  const total = audioComparisons.length;
  const mixingComparisons =
    getAudioComparisonsByCategory("mixing-comparison").length;
  const masteringComparisons = getAudioComparisonsByCategory(
    "mastering-comparison"
  ).length;
  const years = getAllYears();
  const tags = getAllTags();

  return {
    total,
    mixingComparisons,
    masteringComparisons,
    yearsActive: years.length,
    totalTags: tags.length,
    latestYear: years[0],
    oldestYear: years[years.length - 1],
  };
}

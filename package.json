{"name": "noize-capital", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "npx playwright test essential.spec.ts", "test:ui": "npx playwright test essential.spec.ts --ui", "test:headed": "npx playwright test essential.spec.ts --headed", "test:debug": "npx playwright test essential.spec.ts --debug", "test:report": "npx playwright show-report", "test:retry": "node scripts/retry-failed-tests.js", "test:full": "node scripts/test-runner.js", "test:ci": "npx playwright test essential.spec.ts --reporter=json,html && npm run test:retry"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@next/mdx": "^15.3.2", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@sendgrid/mail": "^8.1.5", "@tailwindcss/typography": "^0.5.16", "@types/mdx": "^2.0.13", "@types/three": "^0.176.0", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "esbuild": "^0.19.2", "framer-motion": "^12.10.4", "glob": "^11.0.2", "gray-matter": "^4.0.3", "input-otp": "^1.4.2", "lucide-react": "^0.508.0", "mdx-bundler": "^10.1.1", "next": "15.3.2", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-resizable-panels": "^3.0.1", "recharts": "^2.15.3", "rehype-pretty-code": "^0.14.1", "remark-gfm": "^4.0.1", "remark-mdx-images": "^2.0.0", "shiki": "^3.4.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "three": "^0.176.0", "vaul": "^1.1.2", "wavesurfer.js": "^7.9.9", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^30.0.4", "playwright": "^1.52.0", "tailwindcss": "^4", "ts-jest": "^29.4.0", "tw-animate-css": "^1.2.9", "typescript": "^5"}}
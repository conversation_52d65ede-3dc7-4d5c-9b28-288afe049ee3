"use client";

import { motion, useAnimation } from "framer-motion";
import { useEffect } from "react";

interface TypeWriterProps {
  text: string;
  className?: string;
  delay?: number;
}

export const TypeWriter = ({
  text,
  className = "",
  delay = 0,
}: TypeWriterProps) => {
  const controls = useAnimation();
  const characters = text.split("");

  useEffect(() => {
    const sequence = characters.map((_, i) => ({
      opacity: 1,
      transition: { delay: delay + i * 0.05 },
    }));

    controls.start((i) => sequence[i]);
  }, [characters, controls, delay]);

  return (
    <span className={className}>
      {characters.map((char, i) => (
        <motion.span
          key={i}
          custom={i}
          initial={{ opacity: 0 }}
          animate={controls}
          style={{ display: "inline-block" }}
        >
          {char === " " ? "\u00A0" : char}
        </motion.span>
      ))}
    </span>
  );
};

#!/usr/bin/env node

/**
 * Retry Failed Tests Script
 * 
 * This script automatically retries failed tests from the previous test run.
 * It reads the test results JSON file and re-runs only the failed tests.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const RESULTS_FILE = 'test-results/results.json';
const MAX_RETRIES = 3;

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    retry: '🔄'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function getFailedTests() {
  try {
    if (!fs.existsSync(RESULTS_FILE)) {
      log('No test results file found. Run tests first.', 'warning');
      return [];
    }

    const results = JSON.parse(fs.readFileSync(RESULTS_FILE, 'utf8'));
    const failedTests = [];

    if (results.suites) {
      results.suites.forEach(suite => {
        if (suite.specs) {
          suite.specs.forEach(spec => {
            if (spec.tests) {
              spec.tests.forEach(test => {
                if (test.results) {
                  test.results.forEach(result => {
                    if (result.status === 'failed' || result.status === 'timedOut') {
                      failedTests.push({
                        title: test.title,
                        file: spec.file,
                        project: result.workerIndex !== undefined ? 
                          (result.workerIndex % 2 === 0 ? 'chromium' : 'mobile') : 'chromium',
                        status: result.status,
                        error: result.error?.message || 'Unknown error'
                      });
                    }
                  });
                }
              });
            }
          });
        }
      });
    }

    return failedTests;
  } catch (error) {
    log(`Error reading test results: ${error.message}`, 'error');
    return [];
  }
}

function retryTest(test, retryCount) {
  const testTitle = test.title.replace(/['"]/g, '\\"');
  const command = `npx playwright test "${test.file}" --project="${test.project}" --grep "${testTitle}"`;
  
  log(`Retry ${retryCount}/${MAX_RETRIES}: ${test.title} (${test.project})`, 'retry');
  
  try {
    execSync(command, { 
      stdio: 'inherit',
      timeout: 60000 // 1 minute timeout per test
    });
    log(`✅ Test passed on retry: ${test.title}`, 'success');
    return true;
  } catch (error) {
    log(`❌ Test failed on retry: ${test.title}`, 'error');
    return false;
  }
}

function main() {
  log('🔍 Checking for failed tests...');
  
  const failedTests = getFailedTests();
  
  if (failedTests.length === 0) {
    log('🎉 No failed tests found! All tests are passing.', 'success');
    return;
  }

  log(`Found ${failedTests.length} failed test(s). Starting retry process...`);
  
  const retryResults = {
    total: failedTests.length,
    passed: 0,
    failed: 0,
    details: []
  };

  failedTests.forEach((test, index) => {
    log(`\n📝 Test ${index + 1}/${failedTests.length}: ${test.title}`);
    log(`   File: ${test.file}`);
    log(`   Project: ${test.project}`);
    log(`   Original error: ${test.error.substring(0, 100)}...`);
    
    let passed = false;
    
    for (let retry = 1; retry <= MAX_RETRIES && !passed; retry++) {
      passed = retryTest(test, retry);
      
      if (!passed && retry < MAX_RETRIES) {
        log(`   Waiting 2 seconds before next retry...`);
        execSync('timeout 2 2>nul || sleep 2', { stdio: 'ignore' });
      }
    }
    
    if (passed) {
      retryResults.passed++;
      retryResults.details.push({ test: test.title, status: 'passed' });
    } else {
      retryResults.failed++;
      retryResults.details.push({ test: test.title, status: 'failed' });
    }
  });

  // Summary
  log('\n📊 Retry Summary:');
  log(`   Total tests retried: ${retryResults.total}`);
  log(`   Passed after retry: ${retryResults.passed}`, 'success');
  log(`   Still failing: ${retryResults.failed}`, retryResults.failed > 0 ? 'error' : 'success');
  
  if (retryResults.failed > 0) {
    log('\n❌ Some tests are still failing after retries:');
    retryResults.details
      .filter(detail => detail.status === 'failed')
      .forEach(detail => log(`   - ${detail.test}`, 'error'));
    
    log('\n💡 Consider investigating these persistent failures:', 'warning');
    log('   - Check if the issues are environment-specific');
    log('   - Review test timeouts and selectors');
    log('   - Consider if the test expectations need adjustment');
  } else {
    log('\n🎉 All tests are now passing!', 'success');
  }
  
  // Exit with appropriate code
  process.exit(retryResults.failed > 0 ? 1 : 0);
}

if (require.main === module) {
  main();
}

module.exports = { getFailedTests, retryTest };

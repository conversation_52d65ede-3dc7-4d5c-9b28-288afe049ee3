"use client";

import { useMemo } from "react";
import { Post, PostFrontmatter } from "@/lib/blog";
import { PostCard } from "@/components/blog/PostCard";
import { TagsFilter } from "@/components/blog/TagsFilter";

interface BlogContentProps {
  posts: Post[];
  allTags: string[];
  selectedTag?: string;
}

export function BlogContent({ posts, allTags, selectedTag }: BlogContentProps) {
  // Filter posts based on selected tag
  const filteredPosts = useMemo(() => {
    if (!selectedTag) return posts;

    return posts.filter((post) =>
      post.frontmatter.tags.includes(
        selectedTag as PostFrontmatter["tags"][number]
      )
    );
  }, [posts, selectedTag]);

  return (
    <>
      <TagsFilter tags={allTags} selectedTag={selectedTag} />

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {filteredPosts.map((post) => (
          <PostCard key={post.slug} post={post} />
        ))}
        {filteredPosts.length === 0 && (
          <p className="col-span-full text-center text-muted-foreground py-6 sm:py-8">
            No posts found {selectedTag && `for tag "${selectedTag}"`}
          </p>
        )}
      </div>
    </>
  );
}

import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Upload, List, Settings } from "lucide-react";

export const metadata: Metadata = {
  title: "Admin Panel | Noize Capital",
  description: "Admin panel for managing Noize Capital content",
  robots: "noindex, nofollow",
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Admin Navigation */}
      <nav className="border-b border-white/10 bg-black/50 backdrop-blur-sm">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Site
                </Button>
              </Link>
              <div className="h-6 w-px bg-white/20" />
              <h1 className="text-lg font-semibold text-orange-500">Admin Panel</h1>
            </div>
            
            <div className="flex items-center gap-2">
              <Link href="/admin/audio">
                <Button variant="ghost" size="sm">
                  <Upload className="w-4 h-4 mr-2" />
                  Audio Upload
                </Button>
              </Link>
              <Link href="/admin/manage">
                <Button variant="ghost" size="sm">
                  <List className="w-4 h-4 mr-2" />
                  Manage Files
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main>{children}</main>
    </div>
  );
}

import { NextRequest, NextResponse } from "next/server";
import { promises as fs } from "fs";
import path from "path";
import { AudioComparison } from "@/lib/audio-data";

const DATA_FILE_PATH = path.join(
  process.cwd(),
  "data",
  "audio-comparisons.json"
);

// Ensure data directory exists
async function ensureDataDirectory() {
  const dataDir = path.dirname(DATA_FILE_PATH);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// Read comparisons from JSON file
async function readComparisons(): Promise<AudioComparison[]> {
  try {
    await ensureDataDirectory();
    const data = await fs.readFile(DATA_FILE_PATH, "utf-8");
    return JSON.parse(data);
  } catch (error) {
    // If file doesn't exist or is invalid, return empty array
    return [];
  }
}

// Write comparisons to JSON file
async function writeComparisons(comparisons: AudioComparison[]): Promise<void> {
  await ensureDataDirectory();
  await fs.writeFile(
    DATA_FILE_PATH,
    JSON.stringify(comparisons, null, 2),
    "utf-8"
  );
}

// GET - Retrieve specific audio comparison
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;
    const comparisons = await readComparisons();
    const comparison = comparisons.find((c) => c.id === id);

    if (!comparison) {
      return NextResponse.json(
        { error: "Comparison not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(comparison);
  } catch (error) {
    console.error("Error reading comparison:", error);
    return NextResponse.json(
      { error: "Failed to read comparison" },
      { status: 500 }
    );
  }
}

// PUT - Update existing audio comparison
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;
    const comparisonData = await request.json();
    const comparisons = await readComparisons();

    const index = comparisons.findIndex((c) => c.id === id);
    if (index === -1) {
      return NextResponse.json(
        { error: "Comparison not found" },
        { status: 404 }
      );
    }

    // Validate required fields
    if (!comparisonData.title || !comparisonData.artist) {
      return NextResponse.json(
        { error: "Missing required fields: title, artist" },
        { status: 400 }
      );
    }

    if (!comparisonData.beforeAudio?.url || !comparisonData.afterAudio?.url) {
      return NextResponse.json(
        { error: "Missing required audio URLs" },
        { status: 400 }
      );
    }

    // Update the comparison
    const updatedComparison: AudioComparison = {
      id: id, // Keep the original ID
      title: comparisonData.title,
      artist: comparisonData.artist,
      category: comparisonData.category,
      beforeAudio: {
        id: comparisonData.beforeAudio.id,
        url: comparisonData.beforeAudio.url,
        title: comparisonData.beforeAudio.title,
        artist: comparisonData.beforeAudio.artist,
        type: comparisonData.beforeAudio.type,
        category: comparisonData.beforeAudio.category,
        projectId: comparisonData.beforeAudio.projectId,
        uploadedAt:
          comparisonData.beforeAudio.uploadedAt || new Date().toISOString(),
        tags: comparisonData.beforeAudio.tags || [],
      },
      afterAudio: {
        id: comparisonData.afterAudio.id,
        url: comparisonData.afterAudio.url,
        title: comparisonData.afterAudio.title,
        artist: comparisonData.afterAudio.artist,
        type: comparisonData.afterAudio.type,
        category: comparisonData.afterAudio.category,
        projectId: comparisonData.afterAudio.projectId,
        uploadedAt:
          comparisonData.afterAudio.uploadedAt || new Date().toISOString(),
        tags: comparisonData.afterAudio.tags || [],
      },
      description: comparisonData.description,
      year: comparisonData.year,
      tags: comparisonData.tags || [],
    };

    comparisons[index] = updatedComparison;
    await writeComparisons(comparisons);

    return NextResponse.json(updatedComparison);
  } catch (error) {
    console.error("Error updating comparison:", error);
    return NextResponse.json(
      { error: "Failed to update comparison" },
      { status: 500 }
    );
  }
}

// DELETE - Remove audio comparison
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;
    const comparisons = await readComparisons();
    const index = comparisons.findIndex((c) => c.id === id);

    if (index === -1) {
      return NextResponse.json(
        { error: "Comparison not found" },
        { status: 404 }
      );
    }

    // Remove the comparison
    const deletedComparison = comparisons.splice(index, 1)[0];
    await writeComparisons(comparisons);

    return NextResponse.json({
      message: "Comparison deleted successfully",
      deletedComparison,
    });
  } catch (error) {
    console.error("Error deleting comparison:", error);
    return NextResponse.json(
      { error: "Failed to delete comparison" },
      { status: 500 }
    );
  }
}

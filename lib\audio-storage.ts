// Removed Vercel Blob imports as we're now using URL-based management
import { z } from "zod";

// Audio metadata schema
export const AudioMetadataSchema = z.object({
  id: z.string(),
  title: z.string(),
  artist: z.string(),
  type: z.enum(["mixed", "unmixed", "mastered", "unmastered"]),
  category: z.enum(["mixing-comparison", "mastering-comparison"]),
  projectId: z.string(),
  url: z.string(),
  duration: z.number().optional(),
  fileSize: z.number().optional(),
  uploadedAt: z.string(),
  tags: z.array(z.string()).default([]),
});

export type AudioMetadata = z.infer<typeof AudioMetadataSchema>;

// Audio comparison pair schema
export const AudioComparisonSchema = z.object({
  id: z.string(),
  title: z.string(),
  artist: z.string(),
  category: z.enum(["mixing-comparison", "mastering-comparison"]),
  beforeAudio: AudioMetadataSchema,
  afterAudio: AudioMetadataSchema,
  description: z.string().optional(),
  coverImage: z.string().optional(),
  year: z.number(),
  tags: z.array(z.string()).default([]),
});

export type AudioComparison = z.infer<typeof AudioComparisonSchema>;

// Upload-related functions removed - now using URL-based management

/**
 * Generate audio comparison data structure
 */
export function createAudioComparison(
  title: string,
  artist: string,
  category: "mixing-comparison" | "mastering-comparison",
  beforeMetadata: AudioMetadata,
  afterMetadata: AudioMetadata,
  options?: {
    description?: string;
    coverImage?: string;
    year?: number;
    tags?: string[];
  }
): AudioComparison {
  return {
    id: `${category}-${Date.now()}`,
    title,
    artist,
    category,
    beforeAudio: beforeMetadata,
    afterAudio: afterMetadata,
    description: options?.description,
    coverImage: options?.coverImage,
    year: options?.year || new Date().getFullYear(),
    tags: options?.tags || [],
  };
}

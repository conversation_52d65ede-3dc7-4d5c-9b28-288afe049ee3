/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { motion } from "framer-motion";
import { useAnimations } from "@/hooks/use-animations";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { MapPin, Mail, PhoneCall } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";

// Define form schema
const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  subject: z
    .string()
    .min(5, { message: "Subject must be at least 5 characters" }),
  projectType: z.enum(["mixing", "mastering", "fullPackage", "other"], {
    required_error: "Please select a project type",
  }),
  budget: z.enum(["under500", "500to1000", "1000to2000", "over2000"], {
    required_error: "Please select a budget range",
  }),
  timeline: z.enum(["urgent", "standard", "flexible"], {
    required_error: "Please select a timeline",
  }),
  message: z
    .string()
    .min(10, { message: "Message must be at least 10 characters" }),
});

type FormValues = z.infer<typeof formSchema>;

export default function ContactClient() {
  const { fadeInUp } = useAnimations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>([]);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      subject: "",
      projectType: "mixing",
      budget: "500to1000",
      timeline: "standard",
      message: "",
    },
  });

  // Watch for project type changes and update expanded sections
  useEffect(() => {
    const projectType = form.watch("projectType");

    switch (projectType) {
      case "mixing":
        setExpandedSections(["mixing"]);
        break;
      case "mastering":
        setExpandedSections(["mastering"]);
        break;
      case "fullPackage":
        setExpandedSections(["mixing", "mastering"]);
        break;
      case "other":
        setExpandedSections([]);
        break;
      default:
        setExpandedSections([]);
    }
  }, [form.watch("projectType")]);

  // Form submission handler
  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);

    try {
      // Send data to API endpoint
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Something went wrong");
      }

      // Show success message
      setIsSuccess(true);
      toast.success("Message sent successfully!");

      // Reset form
      form.reset();
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to send message. Please try again."
      );
      console.error("Form submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-16 sm:py-24">
      <motion.div
        className="text-center mb-16"
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
      >
        <h1 className="text-4xl sm:text-5xl font-bold mb-4">Contact Us</h1>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          Get in touch to discuss your project
        </p>
      </motion.div>

      <motion.div
        className="max-w-2xl mx-auto mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{
          opacity: 1,
          y: 0,
          transition: { delay: 0.1, duration: 0.5 },
        }}
      >
        <Accordion
          type="multiple"
          value={expandedSections}
          onValueChange={setExpandedSections}
          className="w-full bg-black/30 backdrop-blur-sm rounded-lg border border-white/10"
        >
          <AccordionItem value="mixing" className="border-white/10">
            <AccordionTrigger className="text-orange-500 hover:text-orange-400 px-4">
              Mixing Project Requirements
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-4 text-gray-300">
              <ul className="list-disc pl-5 space-y-2">
                <li>
                  Consolidated stems (WAV/AIFF format, 24-bit preferred)
                </li>
                <li>Consistent sample rate (44.1kHz or 48kHz)</li>
                <li>Tempo/BPM and key of song</li>
                <li>Rough mix reference (MP3 or WAV)</li>
              </ul>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="mastering" className="border-white/10">
            <AccordionTrigger className="text-orange-500 hover:text-orange-400 px-4">
              Mastering Project Requirements
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-4 text-gray-300">
              <ul className="list-disc pl-5 space-y-2">
                <li>
                  Final stereo mixdown (WAV or AIFF, 24-bit, ideally with -6dB
                  headroom, no limiter on the master bus)
                </li>
                <li>Reference track(s) for loudness/tonal goals</li>
                <li>
                  Preferred loudness target (e.g. Spotify -14 LUFS, Apple
                  Music -16 LUFS)
                </li>
              </ul>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </motion.div>

      <motion.div
        className="max-w-2xl mx-auto bg-black/30 backdrop-blur-sm rounded-lg p-4 sm:p-8 border border-white/10"
        initial={{ opacity: 0, y: 20 }}
        animate={{
          opacity: 1,
          y: 0,
          transition: { delay: 0.2, duration: 0.5 },
        }}
      >
        {isSuccess ? (
          <div className="text-center py-8">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="text-5xl mb-4">✅</div>
              <h3 className="text-2xl font-bold mb-2">Message Sent!</h3>
              <p className="text-gray-300 mb-6">
                Thank you for reaching out. We&apos;ll get back to you as soon
                as possible.
              </p>
              <Button
                onClick={() => setIsSuccess(false)}
                className="bg-orange-500 hover:bg-orange-600"
              >
                Send Another Message
              </Button>
            </motion.div>
          </div>
        ) : (
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-6"
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Your name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subject</FormLabel>
                    <FormControl>
                      <Input placeholder="What's this about?" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-4 border border-white/10 rounded-md p-4 bg-black/20">
                <h3 className="font-medium text-orange-500">
                  Project Details
                </h3>

                <FormField
                  control={form.control}
                  name="projectType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project Type</FormLabel>
                      <FormControl>
                        <select
                          className="w-full bg-black/30 border border-white/20 rounded-md p-2 text-white"
                          {...field}
                        >
                          <option value="mixing">Mixing</option>
                          <option value="mastering">Mastering</option>
                          <option value="fullPackage">
                            Full Package (Mixing & Mastering)
                          </option>
                          <option value="other">Other</option>
                        </select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="budget"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget Range</FormLabel>
                      <FormControl>
                        <select
                          className="w-full bg-black/30 border border-white/20 rounded-md p-2 text-white"
                          {...field}
                        >
                          <option value="under500">Under $500</option>
                          <option value="500to1000">$500 - $1,000</option>
                          <option value="1000to2000">$1,000 - $2,000</option>
                          <option value="over2000">Over $2,000</option>
                        </select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="timeline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Timeline</FormLabel>
                      <FormControl>
                        <select
                          className="w-full bg-black/30 border border-white/20 rounded-md p-2 text-white"
                          {...field}
                        >
                          <option value="urgent">Urgent (1-2 weeks)</option>
                          <option value="standard">
                            Standard (2-4 weeks)
                          </option>
                          <option value="flexible">
                            Flexible (1+ months)
                          </option>
                        </select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Information</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Tell us more about your project or any specific requirements..."
                        rows={4}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full bg-orange-500 hover:bg-orange-600"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Sending..." : "Send Message"}
              </Button>
            </form>
          </Form>
        )}
      </motion.div>

      {/* Contact Quick Links */}
      <motion.div
        className="flex flex-wrap justify-center gap-4 mt-12 mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Button
          variant="outline"
          className="bg-black/30 border-orange-500 hover:bg-orange-500/20"
          onClick={() => window.open("https://wa.me/+27614073833", "_blank")}
        >
          <PhoneCall className="mr-2 h-4 w-4" />
          WhatsApp Us
        </Button>
        <Button
          variant="outline"
          className="bg-black/30 border-orange-500 hover:bg-orange-500/20"
          onClick={() =>
            (window.location.href = "mailto:<EMAIL>")
          }
        >
          <Mail className="mr-2 h-4 w-4" />
          Email Us
        </Button>
      </motion.div>

      {/* Location Section */}
      <motion.div
        className="text-center mt-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="inline-block relative">
          <motion.div
            animate={{
              y: [0, -10, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <MapPin className="h-8 w-8 text-orange-500 mx-auto mb-2" />
          </motion.div>
        </div>
        <p className="text-gray-300">Based in SA, working worldwide</p>
      </motion.div>
    </div>
  );
}

"use client";

import { motion } from "framer-motion";
import { useAnimations } from "@/hooks/use-animations";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";
import { SpotifyEmbed } from "@/components/SpotifyEmbed";
import { AudioComparison } from "@/components/AudioComparison";
import { AudioComparisonWithWaveform } from "@/components/AudioComparisonWithWaveform";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  getAudioComparisonsByCategory,
  getAudioComparisonStats,
} from "@/lib/audio-data";
import { Music, Headphones, TrendingUp, Award, Waves } from "lucide-react";

export default function PortfolioClient() {
  const { fadeInUp, staggerChildren, cardHover, hasReducedMotion } =
    useAnimations();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"comparisons" | "spotify">(
    "comparisons"
  );
  const [showWaveforms, setShowWaveforms] = useState(true);

  // Portfolio projects data
  const projects = [
    {
      id: 1,
      title: "Half Past, Jampas",
      artist: "T-Status feat. Stat",
      image: "/project1.jpg",
      categories: ["Mixing", "Mastering", "Production"],
      year: 2025,
      audioType: "spotify",
      audioSrc: "6DIVhbOsM9Api8smo20OiG", // Spotify track ID
    },
    {
      id: 2,
      title: "Thixo",
      artist: "T-Status",
      image: "/project2.jpg",
      categories: ["Mixing", "Mastering", "Production"],
      year: 2025,
      audioType: "spotify",
      audioSrc: "61On5W0ZtCdxz1Cori0FmC", // Spotify track ID
    },
    {
      id: 3,
      title: "Umdeni",
      artist: "T-Status",
      image: "/project3.jpg",
      categories: ["Mixing", "Mastering", "Production"],
      year: 2023,
      audioType: "spotify",
      audioSrc: "39TijP7Kas1zYPg0Px9g8U", // Spotify track ID
    },
    {
      id: 4,
      title: "Amadoda",
      artist: "KilloTronix feat. Nkosana Mkhonza & Vuvu The Drummer",
      image: "/project4.jpg",
      categories: ["Mixing", "Mastering"],
      year: 2023,
      audioType: "spotify",
      audioSrc: "4znGkNpME2P79gyc0FeLVX", // Spotify track ID
    },
  ];

  // Project detail modal state - used in the onClick handler
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedProject, setSelectedProject] = useState<
    (typeof projects)[0] | null
  >(null);

  // All unique categories
  const categories = Array.from(
    new Set(projects.flatMap((project) => project.categories))
  );

  // Filter projects by category
  const filteredProjects = selectedCategory
    ? projects.filter((project) =>
        project.categories.includes(selectedCategory)
      )
    : projects;

  // Get statistics for the overview
  const stats = getAudioComparisonStats();
  const mixingComparisons = getAudioComparisonsByCategory("mixing-comparison");
  const masteringComparisons = getAudioComparisonsByCategory(
    "mastering-comparison"
  );

  return (
    <div className="container mx-auto px-4 py-16 sm:py-24">
      {/* Hero Section */}
      <motion.div
        className="text-center mb-16"
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
      >
        <h1 className="text-4xl sm:text-5xl font-bold mb-4">Our Portfolio</h1>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto mb-8">
          Experience the transformation. Compare before and after versions of
          our professional mixing and mastering work.
        </p>

        {/* Stats Overview */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto"
          variants={staggerChildren}
        >
          <motion.div
            variants={fadeInUp}
            className="bg-black/30 rounded-lg p-4"
          >
            <div className="flex items-center justify-center mb-2">
              <Music className="w-5 h-5 text-orange-500" />
            </div>
            <div className="text-2xl font-bold text-white">{stats.total}</div>
            <div className="text-xs text-gray-400">Total Projects</div>
          </motion.div>
          <motion.div
            variants={fadeInUp}
            className="bg-black/30 rounded-lg p-4"
          >
            <div className="flex items-center justify-center mb-2">
              <Headphones className="w-5 h-5 text-orange-500" />
            </div>
            <div className="text-2xl font-bold text-white">
              {stats.mixingComparisons}
            </div>
            <div className="text-xs text-gray-400">Mixing Examples</div>
          </motion.div>
          <motion.div
            variants={fadeInUp}
            className="bg-black/30 rounded-lg p-4"
          >
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="w-5 h-5 text-orange-500" />
            </div>
            <div className="text-2xl font-bold text-white">
              {stats.masteringComparisons}
            </div>
            <div className="text-xs text-gray-400">Mastering Examples</div>
          </motion.div>
          <motion.div
            variants={fadeInUp}
            className="bg-black/30 rounded-lg p-4"
          >
            <div className="flex items-center justify-center mb-2">
              <Award className="w-5 h-5 text-orange-500" />
            </div>
            <div className="text-2xl font-bold text-white">
              {stats.yearsActive}
            </div>
            <div className="text-xs text-gray-400">Years Active</div>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Main Content Tabs */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
        className="mb-12"
      >
        <Tabs
          value={activeTab}
          onValueChange={(value) =>
            setActiveTab(value as "comparisons" | "spotify")
          }
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto mb-8">
            <TabsTrigger
              value="comparisons"
              className="flex items-center gap-2"
            >
              <Headphones className="w-4 h-4" />
              Audio Comparisons
            </TabsTrigger>
            <TabsTrigger value="spotify" className="flex items-center gap-2">
              <Music className="w-4 h-4" />
              Spotify Releases
            </TabsTrigger>
          </TabsList>

          {/* Audio Comparisons Tab */}
          <TabsContent value="comparisons" className="space-y-12">
            {/* Mixing Comparisons Section */}
            <motion.section
              initial="hidden"
              animate="visible"
              variants={staggerChildren}
            >
              <motion.div variants={fadeInUp} className="text-center mb-8">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <h2 className="text-3xl font-bold">Mixing Comparisons</h2>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowWaveforms(!showWaveforms)}
                    className="border-white/20 hover:border-orange-500 hover:text-orange-500"
                  >
                    <Waves className="w-4 h-4 mr-2" />
                    {showWaveforms ? "Hide" : "Show"} Waveforms
                  </Button>
                </div>
                <p className="text-gray-300 max-w-2xl mx-auto">
                  Hear the difference professional mixing makes. Compare raw
                  recordings with their polished, balanced final versions.
                </p>
              </motion.div>

              <div className="grid gap-8 lg:gap-12">
                {mixingComparisons.map((comparison, index) => (
                  <motion.div
                    key={comparison.id}
                    variants={fadeInUp}
                    custom={index}
                  >
                    {showWaveforms ? (
                      <AudioComparisonWithWaveform
                        comparison={comparison}
                        showWaveform={true}
                      />
                    ) : (
                      <AudioComparison comparison={comparison} />
                    )}
                  </motion.div>
                ))}
              </div>
            </motion.section>

            {/* Mastering Comparisons Section */}
            <motion.section
              initial="hidden"
              animate="visible"
              variants={staggerChildren}
            >
              <motion.div variants={fadeInUp} className="text-center mb-8">
                <h2 className="text-3xl font-bold mb-4">
                  Mastering Comparisons
                </h2>
                <p className="text-gray-300 max-w-2xl mx-auto">
                  Experience the final polish. Compare mixed tracks with their
                  mastered versions ready for commercial release.
                </p>
              </motion.div>

              <div className="grid gap-8 lg:gap-12">
                {masteringComparisons.map((comparison, index) => (
                  <motion.div
                    key={comparison.id}
                    variants={fadeInUp}
                    custom={index}
                  >
                    {showWaveforms ? (
                      <AudioComparisonWithWaveform
                        comparison={comparison}
                        showWaveform={true}
                      />
                    ) : (
                      <AudioComparison comparison={comparison} />
                    )}
                  </motion.div>
                ))}
              </div>
            </motion.section>
          </TabsContent>

          {/* Spotify Releases Tab */}
          <TabsContent value="spotify">
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="text-center mb-8"
            >
              <h2 className="text-3xl font-bold mb-4">Spotify Releases</h2>
              <p className="text-gray-300 max-w-2xl mx-auto">
                Listen to our completed projects on Spotify and explore the
                artists we&apos;ve collaborated with.
              </p>
            </motion.div>

            {/* Category filters for Spotify releases */}
            <motion.div
              className="flex flex-wrap justify-center gap-2 mb-12"
              initial="hidden"
              animate="visible"
              variants={staggerChildren}
            >
              <motion.div variants={fadeInUp}>
                <Button
                  variant={selectedCategory === null ? "default" : "outline"}
                  onClick={() => setSelectedCategory(null)}
                  className={
                    selectedCategory === null
                      ? "bg-orange-500 hover:bg-orange-600"
                      : "border-white/20 hover:border-orange-500 hover:text-orange-500"
                  }
                >
                  All Projects
                </Button>
              </motion.div>
              {categories.map((category) => (
                <motion.div key={category} variants={fadeInUp}>
                  <Button
                    variant={
                      selectedCategory === category ? "default" : "outline"
                    }
                    onClick={() => setSelectedCategory(category)}
                    className={
                      selectedCategory === category
                        ? "bg-orange-500 hover:bg-orange-600"
                        : "border-white/20 hover:border-orange-500 hover:text-orange-500"
                    }
                  >
                    {category}
                  </Button>
                </motion.div>
              ))}
            </motion.div>

            {/* Spotify Projects grid */}
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8"
              initial="hidden"
              animate="visible"
              variants={staggerChildren}
            >
              {filteredProjects.map((project) => (
                <motion.div
                  key={project.id}
                  variants={fadeInUp}
                  whileHover={cardHover}
                  className="h-full"
                >
                  <Card className="h-full border border-white/10 bg-black/50 backdrop-blur-sm overflow-hidden">
                    <div className="relative h-48 w-full overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent z-10" />
                      <Image
                        src={project.image}
                        alt={`${project.title} by ${project.artist}`}
                        fill
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        priority={project.id <= 2}
                        className="object-cover"
                        style={{ position: "absolute" }}
                      />
                    </div>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-xl">
                            {project.title}
                          </CardTitle>
                          <CardDescription>{project.artist}</CardDescription>
                        </div>
                        <Badge variant="outline">{project.year}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {project.categories.map((category) => (
                          <Badge
                            key={category}
                            variant="secondary"
                            className="text-xs"
                          >
                            {category}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                    <CardFooter className="flex flex-col gap-4">
                      {project.audioType === "spotify" && (
                        <SpotifyEmbed
                          spotifyId={project.audioSrc}
                          type="track"
                          className="w-full"
                        />
                      )}

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={() => setSelectedProject(project)}
                          >
                            View Details
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="bg-black/90 backdrop-blur-md border-white/10 max-w-3xl">
                          <DialogHeader>
                            <DialogTitle className="text-2xl">
                              {project.title} - {project.artist}
                            </DialogTitle>
                          </DialogHeader>

                          <div className="mt-4 space-y-6">
                            {/* Project image */}
                            <div className="relative w-full h-48 rounded-md overflow-hidden">
                              <Image
                                src={project.image}
                                alt={`${project.title} by ${project.artist}`}
                                fill
                                sizes="(max-width: 1200px) 100vw, 50vw"
                                className="object-cover"
                                style={{ position: "absolute" }}
                              />
                            </div>

                            {/* Project details */}
                            <div className="space-y-4">
                              <div>
                                <h3 className="text-lg font-medium text-orange-500">
                                  Services provided
                                </h3>
                                <div className="flex flex-wrap gap-2 mt-2">
                                  {project.categories.map((category) => (
                                    <Badge key={category} variant="secondary">
                                      {category}
                                    </Badge>
                                  ))}
                                </div>
                              </div>

                              {/* Spotify embed */}
                              {project.audioType === "spotify" && (
                                <div>
                                  <h3 className="text-lg font-medium text-orange-500 mb-2">
                                    Listen
                                  </h3>
                                  <SpotifyEmbed
                                    spotifyId={project.audioSrc}
                                    type="track"
                                    height={152}
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>
        </Tabs>
      </motion.div>

      {/* Call to action */}
      <motion.div
        className="text-center mt-20"
        initial={{ opacity: 0 }}
        animate={{
          opacity: 1,
          transition: { delay: hasReducedMotion ? 0 : 0.5 },
        }}
      >
        <h2 className="text-2xl font-bold mb-4">
          Ready to create your next project?
        </h2>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          Let&apos;s collaborate to bring your musical vision to life with
          professional quality.
        </p>
        <Button size="lg" className="bg-orange-500 hover:bg-orange-600">
          <Link href="/contact">Start Your Project</Link>
        </Button>
      </motion.div>
    </div>
  );
}

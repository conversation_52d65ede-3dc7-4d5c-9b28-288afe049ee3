import { NextRequest, NextResponse } from "next/server";
import { promises as fs } from "fs";
import path from "path";
import { AudioComparison } from "@/lib/audio-storage";

const DATA_FILE_PATH = path.join(process.cwd(), "data", "audio-comparisons.json");

// Ensure data directory exists
async function ensureDataDirectory() {
  const dataDir = path.dirname(DATA_FILE_PATH);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// Read comparisons from JSON file
async function readComparisons(): Promise<AudioComparison[]> {
  try {
    await ensureDataDirectory();
    const data = await fs.readFile(DATA_FILE_PATH, "utf-8");
    return JSON.parse(data);
  } catch {
    // If file doesn't exist or is invalid, return empty array
    console.log("No existing comparisons file found, starting with empty array");
    return [];
  }
}

// Write comparisons to JSON file
async function writeComparisons(comparisons: AudioComparison[]): Promise<void> {
  await ensureDataDirectory();
  await fs.writeFile(DATA_FILE_PATH, JSON.stringify(comparisons, null, 2), "utf-8");
}

// GET - Retrieve all audio comparisons
export async function GET() {
  try {
    const comparisons = await readComparisons();
    return NextResponse.json(comparisons);
  } catch (error) {
    console.error("Error reading comparisons:", error);
    return NextResponse.json(
      { error: "Failed to read comparisons" },
      { status: 500 }
    );
  }
}

// POST - Add new audio comparison
export async function POST(request: NextRequest) {
  try {
    const comparisonData = await request.json();
    
    // Validate required fields
    if (!comparisonData.id || !comparisonData.title || !comparisonData.artist) {
      return NextResponse.json(
        { error: "Missing required fields: id, title, artist" },
        { status: 400 }
      );
    }

    if (!comparisonData.beforeAudio?.url || !comparisonData.afterAudio?.url) {
      return NextResponse.json(
        { error: "Missing required audio URLs" },
        { status: 400 }
      );
    }

    const comparisons = await readComparisons();
    
    // Check if ID already exists
    if (comparisons.find(c => c.id === comparisonData.id)) {
      return NextResponse.json(
        { error: "Comparison with this ID already exists" },
        { status: 409 }
      );
    }

    // Add the new comparison
    const newComparison: AudioComparison = {
      id: comparisonData.id,
      title: comparisonData.title,
      artist: comparisonData.artist,
      category: comparisonData.category,
      beforeAudio: {
        id: comparisonData.beforeAudio.id,
        url: comparisonData.beforeAudio.url,
        title: comparisonData.beforeAudio.title,
        artist: comparisonData.beforeAudio.artist,
        type: comparisonData.beforeAudio.type,
        category: comparisonData.beforeAudio.category,
        projectId: comparisonData.beforeAudio.projectId,
        uploadedAt: comparisonData.beforeAudio.uploadedAt || new Date().toISOString(),
        tags: comparisonData.beforeAudio.tags || [],
      },
      afterAudio: {
        id: comparisonData.afterAudio.id,
        url: comparisonData.afterAudio.url,
        title: comparisonData.afterAudio.title,
        artist: comparisonData.afterAudio.artist,
        type: comparisonData.afterAudio.type,
        category: comparisonData.afterAudio.category,
        projectId: comparisonData.afterAudio.projectId,
        uploadedAt: comparisonData.afterAudio.uploadedAt || new Date().toISOString(),
        tags: comparisonData.afterAudio.tags || [],
      },
      description: comparisonData.description,
      year: comparisonData.year,
      tags: comparisonData.tags || [],
    };

    comparisons.push(newComparison);
    await writeComparisons(comparisons);

    return NextResponse.json(newComparison, { status: 201 });
  } catch (error) {
    console.error("Error creating comparison:", error);
    return NextResponse.json(
      { error: "Failed to create comparison" },
      { status: 500 }
    );
  }
}

"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useAnimations } from "@/hooks/use-animations";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Music, Trash2, Search, Download, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";

interface AudioFile {
  url: string;
  pathname: string;
  size: number;
  uploadedAt: Date;
}

export default function ManageFilesClient() {
  const { fadeInUp, staggerChildren } = useAnimations();
  const [files, setFiles] = useState<AudioFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [deleting, setDeleting] = useState<string | null>(null);

  const loadFiles = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/audio/list');
      const result = await response.json();
      
      if (result.success) {
        setFiles(result.files.map((file: any) => ({
          ...file,
          uploadedAt: new Date(file.uploadedAt),
        })));
      } else {
        console.error('Failed to load files:', result.error);
      }
    } catch (error) {
      console.error('Error loading files:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFiles();
  }, []);

  const deleteFile = async (url: string) => {
    if (!confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
      return;
    }

    setDeleting(url);
    try {
      const response = await fetch(`/api/audio/upload?url=${encodeURIComponent(url)}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      
      if (result.success) {
        setFiles(prev => prev.filter(file => file.url !== url));
      } else {
        alert('Failed to delete file: ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      alert('Failed to delete file');
    } finally {
      setDeleting(null);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileName = (pathname: string) => {
    return pathname.split('/').pop() || pathname;
  };

  const getFileCategory = (pathname: string) => {
    if (pathname.includes('mixing-comparison')) return 'Mixing';
    if (pathname.includes('mastering-comparison')) return 'Mastering';
    return 'Unknown';
  };

  const getFileType = (pathname: string) => {
    if (pathname.includes('-mixed-')) return 'Mixed';
    if (pathname.includes('-unmixed-')) return 'Unmixed';
    if (pathname.includes('-mastered-')) return 'Mastered';
    if (pathname.includes('-unmastered-')) return 'Unmastered';
    return 'Unknown';
  };

  const filteredFiles = files.filter(file =>
    getFileName(file.pathname).toLowerCase().includes(searchTerm.toLowerCase()) ||
    file.pathname.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="container mx-auto px-4 py-16 sm:py-24">
      <motion.div
        className="max-w-6xl mx-auto"
        initial="hidden"
        animate="visible"
        variants={staggerChildren}
      >
        {/* Header */}
        <motion.div variants={fadeInUp} className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Manage Audio Files</h1>
          <p className="text-xl text-gray-300">
            View, search, and manage your uploaded audio files
          </p>
        </motion.div>

        {/* Controls */}
        <motion.div variants={fadeInUp} className="mb-8">
          <Card className="border-white/10 bg-black/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>File Management</CardTitle>
              <CardDescription>
                Search and manage your audio files
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 items-center">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    placeholder="Search files..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button onClick={loadFiles} variant="outline" disabled={loading}>
                  <RefreshCw className={cn("w-4 h-4 mr-2", loading && "animate-spin")} />
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Files Table */}
        <motion.div variants={fadeInUp}>
          <Card className="border-white/10 bg-black/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Music className="w-5 h-5" />
                Audio Files ({filteredFiles.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
                  <p className="text-gray-400">Loading files...</p>
                </div>
              ) : filteredFiles.length === 0 ? (
                <div className="text-center py-8">
                  <Music className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-400">
                    {searchTerm ? 'No files match your search' : 'No audio files uploaded yet'}
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>File Name</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Size</TableHead>
                        <TableHead>Uploaded</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredFiles.map((file) => (
                        <TableRow key={file.url}>
                          <TableCell className="font-medium">
                            {getFileName(file.pathname)}
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {getFileCategory(file.pathname)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {getFileType(file.pathname)}
                            </Badge>
                          </TableCell>
                          <TableCell>{formatFileSize(file.size)}</TableCell>
                          <TableCell>
                            {file.uploadedAt.toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.open(file.url, '_blank')}
                              >
                                <Download className="w-4 h-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => deleteFile(file.url)}
                                disabled={deleting === file.url}
                              >
                                {deleting === file.url ? (
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                ) : (
                                  <Trash2 className="w-4 h-4" />
                                )}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Statistics */}
        <motion.div variants={fadeInUp} className="mt-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="border-white/10 bg-black/50 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-orange-500 mb-2">
                  {files.length}
                </div>
                <div className="text-sm text-gray-400">Total Files</div>
              </CardContent>
            </Card>
            <Card className="border-white/10 bg-black/50 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-orange-500 mb-2">
                  {formatFileSize(files.reduce((total, file) => total + file.size, 0))}
                </div>
                <div className="text-sm text-gray-400">Total Size</div>
              </CardContent>
            </Card>
            <Card className="border-white/10 bg-black/50 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-orange-500 mb-2">
                  {files.filter(file => file.pathname.includes('mixing-comparison')).length}
                </div>
                <div className="text-sm text-gray-400">Mixing Files</div>
              </CardContent>
            </Card>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}

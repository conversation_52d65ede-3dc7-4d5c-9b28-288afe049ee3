import { Suspense } from "react";
import { Metadata } from "next";
import { getPosts, getAllTags } from "@/lib/blog";
import { BlogContent } from "@/components/blog/BlogContent";

export const dynamic = "force-static";

// Generate metadata for the blog page
export const metadata: Metadata = {
  title: "Blog | Noize Capital",
  description:
    "Insights, tutorials, and guides about music production, mixing, and mastering",
  keywords: [
    "music production",
    "mixing",
    "mastering",
    "audio engineering",
    "tutorials",
  ],
  openGraph: {
    title: "Blog | Noize Capital",
    description:
      "Insights, tutorials, and guides about music production, mixing, and mastering",
    type: "website",
  },
};

type BlogPageProps = {
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
};

export default async function BlogPage({ searchParams }: BlogPageProps) {
  // Load posts and tags on the server
  const posts = await getPosts();
  const allTags = getAllTags(posts);

  // Get search params
  const resolvedSearchParams = await searchParams;
  const selectedTag = resolvedSearchParams?.tag as string | undefined;

  return (
    <main className="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
      <div className="space-y-6 sm:space-y-8">
        <div className="text-center space-y-3 sm:space-y-4">
          <h1 className="text-3xl sm:text-4xl font-bold">Blog</h1>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto px-2">
            Insights, tutorials, and guides about music production, mixing, and
            mastering
          </p>
        </div>

        <Suspense fallback={<div>Loading content...</div>}>
          <BlogContent
            posts={posts}
            allTags={allTags}
            selectedTag={selectedTag}
          />
        </Suspense>
      </div>
    </main>
  );
}

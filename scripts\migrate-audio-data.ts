import { promises as fs } from "fs";
import path from "path";
import { audioComparisons } from "../lib/audio-data";

const DATA_FILE_PATH = path.join(process.cwd(), "data", "audio-comparisons.json");

async function migrateAudioData() {
  try {
    // Ensure data directory exists
    const dataDir = path.dirname(DATA_FILE_PATH);
    try {
      await fs.access(dataDir);
    } catch {
      await fs.mkdir(dataDir, { recursive: true });
    }

    // Check if file already exists
    try {
      await fs.access(DATA_FILE_PATH);
      console.log("Audio comparisons file already exists. Skipping migration.");
      return;
    } catch {
      // File doesn't exist, proceed with migration
    }

    // Write the existing data to JSON file
    await fs.writeFile(
      DATA_FILE_PATH,
      JSON.stringify(audioComparisons, null, 2),
      "utf-8"
    );

    console.log(`Successfully migrated ${audioComparisons.length} audio comparisons to ${DATA_FILE_PATH}`);
  } catch (error) {
    console.error("Error migrating audio data:", error);
    process.exit(1);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  migrateAudioData();
}

export { migrateAudioData };

"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Play, Pause, Volume2, VolumeX, RotateCcw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { AudioComparison as AudioComparisonType } from "@/lib/audio-storage";

interface AudioComparisonProps {
  comparison: AudioComparisonType;
  className?: string;
}

export function AudioComparison({ comparison, className }: AudioComparisonProps) {
  const [activeVersion, setActiveVersion] = useState<'before' | 'after'>('before');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(0.7);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  const beforeAudioRef = useRef<HTMLAudioElement>(null);
  const afterAudioRef = useRef<HTMLAudioElement>(null);

  const currentAudioRef = activeVersion === 'before' ? beforeAudioRef : afterAudioRef;

  // Load audio metadata
  useEffect(() => {
    const audio = currentAudioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      setIsLoaded(true);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      audio.currentTime = 0;
    };

    audio.addEventListener("loadedmetadata", handleLoadedMetadata);
    audio.addEventListener("timeupdate", handleTimeUpdate);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
      audio.removeEventListener("timeupdate", handleTimeUpdate);
      audio.removeEventListener("ended", handleEnded);
    };
  }, [activeVersion, currentAudioRef]);

  // Handle play/pause
  useEffect(() => {
    const beforeAudio = beforeAudioRef.current;
    const afterAudio = afterAudioRef.current;
    
    if (!beforeAudio || !afterAudio) return;

    if (isPlaying) {
      // Pause the inactive audio and sync time
      const inactiveAudio = activeVersion === 'before' ? afterAudio : beforeAudio;
      const activeAudio = currentAudioRef.current;
      
      inactiveAudio.pause();
      inactiveAudio.currentTime = currentTime;
      
      if (activeAudio) {
        activeAudio.play().catch((error) => {
          console.error("Error playing audio:", error);
          setIsPlaying(false);
        });
      }
    } else {
      beforeAudio.pause();
      afterAudio.pause();
    }
  }, [isPlaying, activeVersion, currentTime, currentAudioRef]);

  // Handle volume change
  useEffect(() => {
    const beforeAudio = beforeAudioRef.current;
    const afterAudio = afterAudioRef.current;
    
    if (!beforeAudio || !afterAudio) return;

    const volumeValue = isMuted ? 0 : volume;
    beforeAudio.volume = volumeValue;
    afterAudio.volume = volumeValue;
  }, [volume, isMuted]);

  // Switch between versions while maintaining playback position
  const switchVersion = (version: 'before' | 'after') => {
    if (version === activeVersion) return;

    const currentAudio = currentAudioRef.current;
    const wasPlaying = isPlaying;
    const currentPosition = currentTime;

    // Pause current audio
    if (currentAudio) {
      currentAudio.pause();
    }

    // Switch version
    setActiveVersion(version);

    // Sync the new audio to the same position
    setTimeout(() => {
      const newAudio = version === 'before' ? beforeAudioRef.current : afterAudioRef.current;
      if (newAudio) {
        newAudio.currentTime = currentPosition;
        if (wasPlaying) {
          newAudio.play().catch(console.error);
        }
      }
    }, 50);
  };

  // Format time (seconds -> mm:ss)
  const formatTime = (time: number) => {
    if (isNaN(time)) return "00:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  // Handle seek
  const handleSeek = (value: number[]) => {
    const newTime = value[0];
    const beforeAudio = beforeAudioRef.current;
    const afterAudio = afterAudioRef.current;
    
    if (beforeAudio && afterAudio) {
      beforeAudio.currentTime = newTime;
      afterAudio.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  // Reset to beginning
  const resetAudio = () => {
    const beforeAudio = beforeAudioRef.current;
    const afterAudio = afterAudioRef.current;
    
    if (beforeAudio && afterAudio) {
      beforeAudio.currentTime = 0;
      afterAudio.currentTime = 0;
      setCurrentTime(0);
      setIsPlaying(false);
    }
  };

  const getVersionLabel = () => {
    if (comparison.category === 'mixing-comparison') {
      return activeVersion === 'before' ? 'Unmixed' : 'Mixed';
    } else {
      return activeVersion === 'before' ? 'Unmastered' : 'Mastered';
    }
  };

  const getVersionDescription = () => {
    if (comparison.category === 'mixing-comparison') {
      return activeVersion === 'before' 
        ? 'Raw recording before mixing process'
        : 'Professional mix with balanced levels and effects';
    } else {
      return activeVersion === 'before'
        ? 'Mixed track before mastering'
        : 'Final mastered version ready for release';
    }
  };

  return (
    <div className={cn("bg-black/40 backdrop-blur-sm rounded-lg border border-white/10 p-6", className)}>
      {/* Hidden audio elements */}
      <audio ref={beforeAudioRef} preload="metadata">
        <source src={comparison.beforeAudio.id} type="audio/mpeg" />
      </audio>
      <audio ref={afterAudioRef} preload="metadata">
        <source src={comparison.afterAudio.id} type="audio/mpeg" />
      </audio>

      {/* Header */}
      <div className="mb-6">
        <div className="flex items-start justify-between mb-2">
          <div>
            <h3 className="text-xl font-bold text-white">{comparison.title}</h3>
            <p className="text-gray-300">{comparison.artist}</p>
          </div>
          <Badge variant="outline" className="text-orange-500 border-orange-500">
            {comparison.category === 'mixing-comparison' ? 'Mixing' : 'Mastering'}
          </Badge>
        </div>
        {comparison.description && (
          <p className="text-sm text-gray-400">{comparison.description}</p>
        )}
      </div>

      {/* Version Switcher */}
      <div className="mb-6">
        <div className="flex bg-black/30 rounded-lg p-1 mb-3">
          <Button
            variant={activeVersion === 'before' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => switchVersion('before')}
            className={cn(
              "flex-1 transition-all duration-200",
              activeVersion === 'before' 
                ? "bg-orange-500 hover:bg-orange-600 text-white" 
                : "text-gray-300 hover:text-white hover:bg-white/10"
            )}
          >
            {comparison.category === 'mixing-comparison' ? 'Unmixed' : 'Unmastered'}
          </Button>
          <Button
            variant={activeVersion === 'after' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => switchVersion('after')}
            className={cn(
              "flex-1 transition-all duration-200",
              activeVersion === 'after' 
                ? "bg-orange-500 hover:bg-orange-600 text-white" 
                : "text-gray-300 hover:text-white hover:bg-white/10"
            )}
          >
            {comparison.category === 'mixing-comparison' ? 'Mixed' : 'Mastered'}
          </Button>
        </div>

        {/* Current version info */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeVersion}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="text-center"
          >
            <p className="text-sm font-medium text-orange-500">{getVersionLabel()}</p>
            <p className="text-xs text-gray-400">{getVersionDescription()}</p>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Audio Controls */}
      <div className="space-y-4">
        {/* Main controls */}
        <div className="flex items-center gap-4">
          <Button
            onClick={() => setIsPlaying(!isPlaying)}
            disabled={!isLoaded}
            size="lg"
            className="bg-orange-500 hover:bg-orange-600 text-white rounded-full w-12 h-12 flex-shrink-0"
          >
            {isPlaying ? <Pause size={20} /> : <Play size={20} />}
          </Button>

          <Button
            onClick={resetAudio}
            variant="outline"
            size="sm"
            className="flex-shrink-0"
          >
            <RotateCcw size={16} />
          </Button>

          <div className="text-xs text-gray-400 w-12 flex-shrink-0">
            {formatTime(currentTime)}
          </div>

          <div className="flex-grow">
            <Slider
              value={[currentTime]}
              min={0}
              max={duration || 100}
              step={0.1}
              onValueChange={handleSeek}
              disabled={!isLoaded}
              className="w-full"
            />
          </div>

          <div className="text-xs text-gray-400 w-12 flex-shrink-0 text-right">
            {formatTime(duration)}
          </div>
        </div>

        {/* Volume control */}
        <div className="flex items-center gap-3">
          <Button
            onClick={() => setIsMuted(!isMuted)}
            variant="ghost"
            size="sm"
            className="flex-shrink-0"
          >
            {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
          </Button>
          <div className="flex-grow max-w-24">
            <Slider
              value={[volume]}
              min={0}
              max={1}
              step={0.01}
              onValueChange={(value) => {
                setVolume(value[0]);
                if (value[0] > 0 && isMuted) {
                  setIsMuted(false);
                }
              }}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Tags */}
      {comparison.tags.length > 0 && (
        <div className="mt-4 flex flex-wrap gap-2">
          {comparison.tags.map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}

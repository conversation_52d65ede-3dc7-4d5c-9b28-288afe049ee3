"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Play, Pause, Volume2, VolumeX, RotateCcw, Waves } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { AudioComparison as AudioComparisonType } from "@/lib/audio-storage";
import WaveSurfer from "wavesurfer.js";

interface AudioComparisonWithWaveformProps {
  comparison: AudioComparisonType;
  className?: string;
  showWaveform?: boolean;
}

export function AudioComparisonWithWaveform({
  comparison,
  className,
  showWaveform = true,
}: AudioComparisonWithWaveformProps) {
  const [activeVersion, setActiveVersion] = useState<"before" | "after">(
    "before"
  );
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(0.7);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [waveformLoaded, setWaveformLoaded] = useState(false);

  const beforeWaveformRef = useRef<HTMLDivElement>(null);
  const afterWaveformRef = useRef<HTMLDivElement>(null);
  const beforeWaveSurferRef = useRef<WaveSurfer | null>(null);
  const afterWaveSurferRef = useRef<WaveSurfer | null>(null);

  const currentWaveSurfer =
    activeVersion === "before"
      ? beforeWaveSurferRef.current
      : afterWaveSurferRef.current;

  // Check if audio URLs are valid (not placeholder URLs)
  const hasValidAudioUrls =
    comparison.beforeAudio.url.startsWith("http") &&
    comparison.afterAudio.url.startsWith("http") &&
    !comparison.beforeAudio.url.includes("placeholder") &&
    !comparison.afterAudio.url.includes("example.com");

  // Initialize WaveSurfer instances
  useEffect(() => {
    if (!showWaveform || !hasValidAudioUrls) {
      setIsLoaded(true);
      return;
    }

    let isMounted = true;

    const initWaveSurfer = async () => {
      try {
        // Initialize before waveform
        if (
          beforeWaveformRef.current &&
          !beforeWaveSurferRef.current &&
          isMounted
        ) {
          beforeWaveSurferRef.current = WaveSurfer.create({
            container: beforeWaveformRef.current,
            waveColor: "#6b7280",
            progressColor: "#f97316",
            cursorColor: "#f97316",
            barWidth: 2,
            barRadius: 1,
            height: 60,
            normalize: true,
            backend: "WebAudio",
          });

          if (isMounted) {
            await beforeWaveSurferRef.current.load(comparison.beforeAudio.url);
          }
        }

        // Initialize after waveform
        if (
          afterWaveformRef.current &&
          !afterWaveSurferRef.current &&
          isMounted
        ) {
          afterWaveSurferRef.current = WaveSurfer.create({
            container: afterWaveformRef.current,
            waveColor: "#6b7280",
            progressColor: "#f97316",
            cursorColor: "#f97316",
            barWidth: 2,
            barRadius: 1,
            height: 60,
            normalize: true,
            backend: "WebAudio",
          });

          if (isMounted) {
            await afterWaveSurferRef.current.load(comparison.afterAudio.url);
          }
        }

        if (isMounted) {
          setWaveformLoaded(true);
          setIsLoaded(true);
        }
      } catch (error) {
        console.error("Error initializing waveforms:", error);
        setIsLoaded(true); // Still allow basic functionality
      }
    };

    initWaveSurfer();

    return () => {
      isMounted = false;
      beforeWaveSurferRef.current?.destroy();
      afterWaveSurferRef.current?.destroy();
    };
  }, [comparison, showWaveform, hasValidAudioUrls]);

  // Set up event listeners for current waveform
  useEffect(() => {
    if (!currentWaveSurfer) return;

    const handleReady = () => {
      setDuration(currentWaveSurfer.getDuration());
    };

    const handleAudioProcess = () => {
      setCurrentTime(currentWaveSurfer.getCurrentTime());
    };

    const handleFinish = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    currentWaveSurfer.on("ready", handleReady);
    currentWaveSurfer.on("audioprocess", handleAudioProcess);
    currentWaveSurfer.on("finish", handleFinish);

    return () => {
      currentWaveSurfer.un("ready", handleReady);
      currentWaveSurfer.un("audioprocess", handleAudioProcess);
      currentWaveSurfer.un("finish", handleFinish);
    };
  }, [currentWaveSurfer]);

  // Sync volume and mute state
  useEffect(() => {
    if (currentWaveSurfer) {
      currentWaveSurfer.setVolume(isMuted ? 0 : volume);
    }
  }, [currentWaveSurfer, volume, isMuted]);

  const togglePlayPause = () => {
    if (!currentWaveSurfer) return;

    if (isPlaying) {
      currentWaveSurfer.pause();
    } else {
      currentWaveSurfer.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (value: number[]) => {
    const time = value[0];
    if (currentWaveSurfer) {
      currentWaveSurfer.seekTo(time / duration);
      setCurrentTime(time);
    }
  };

  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setVolume(newVolume);
    if (currentWaveSurfer) {
      currentWaveSurfer.setVolume(isMuted ? 0 : newVolume);
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const resetPlayback = () => {
    if (currentWaveSurfer) {
      currentWaveSurfer.seekTo(0);
      setCurrentTime(0);
      if (isPlaying) {
        currentWaveSurfer.pause();
        setIsPlaying(false);
      }
    }
  };

  const switchVersion = (version: "before" | "after") => {
    if (version === activeVersion) return;

    // Store current playback state and position
    const wasPlaying = isPlaying;
    const currentPosition = currentTime;

    // Get the current WaveSurfer instance before switching
    const oldWaveSurfer =
      activeVersion === "before"
        ? beforeWaveSurferRef.current
        : afterWaveSurferRef.current;

    // Get the new WaveSurfer instance
    const newWaveSurfer =
      version === "before"
        ? beforeWaveSurferRef.current
        : afterWaveSurferRef.current;

    // Pause current audio
    if (oldWaveSurfer && isPlaying) {
      oldWaveSurfer.pause();
      setIsPlaying(false);
    }

    // Switch to new version
    setActiveVersion(version);

    // Immediately try to restore playback state
    if (newWaveSurfer && hasValidAudioUrls) {
      try {
        // Seek to the same position
        if (duration > 0 && currentPosition > 0) {
          const seekPosition = Math.min(currentPosition / duration, 0.99);
          newWaveSurfer.seekTo(seekPosition);
        }

        // Resume playback if it was playing before
        if (wasPlaying) {
          // Use a small timeout to ensure the seek has completed
          setTimeout(() => {
            newWaveSurfer
              .play()
              .then(() => {
                setIsPlaying(true);
              })
              .catch((error) => {
                console.error("Error resuming playback:", error);
                setIsPlaying(false);
              });
          }, 50);
        }
      } catch (error) {
        console.error("Error switching version:", error);
      }
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const getVersionLabel = (version: "before" | "after") => {
    if (comparison.category === "mixing-comparison") {
      return version === "before" ? "Unmixed" : "Mixed";
    } else {
      return version === "before" ? "Unmastered" : "Mastered";
    }
  };

  const getVersionDescription = (version: "before" | "after") => {
    if (comparison.category === "mixing-comparison") {
      return version === "before"
        ? "Raw recording with individual tracks"
        : "Professionally mixed and balanced";
    } else {
      return version === "before"
        ? "Mixed but not mastered"
        : "Final mastered version";
    }
  };

  return (
    <Card
      className={cn("border-white/10 bg-black/50 backdrop-blur-sm", className)}
    >
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl mb-2">{comparison.title}</CardTitle>
            <p className="text-gray-300">{comparison.artist}</p>
          </div>
          <Badge variant="secondary" className="capitalize">
            {comparison.category.replace("-", " ")}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Version Toggle */}
        <div className="flex gap-2">
          <Button
            variant={activeVersion === "before" ? "default" : "outline"}
            onClick={() => switchVersion("before")}
            className={cn(
              "flex-1",
              activeVersion === "before"
                ? "bg-orange-500 hover:bg-orange-600"
                : "border-white/20 hover:border-orange-500 hover:text-orange-500"
            )}
          >
            {getVersionLabel("before")}
          </Button>
          <Button
            variant={activeVersion === "after" ? "default" : "outline"}
            onClick={() => switchVersion("after")}
            className={cn(
              "flex-1",
              activeVersion === "after"
                ? "bg-orange-500 hover:bg-orange-600"
                : "border-white/20 hover:border-orange-500 hover:text-orange-500"
            )}
          >
            {getVersionLabel("after")}
          </Button>
        </div>

        {/* Current Version Info */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeVersion}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="text-center p-4 bg-white/5 rounded-lg"
          >
            <h4 className="font-medium text-orange-500 mb-1">
              {getVersionLabel(activeVersion)}
            </h4>
            <p className="text-sm text-gray-400">
              {getVersionDescription(activeVersion)}
            </p>
          </motion.div>
        </AnimatePresence>

        {/* Waveform Visualization */}
        {showWaveform && (
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Waves className="w-4 h-4" />
              <span>Waveform Visualization</span>
            </div>

            <div className="relative">
              {hasValidAudioUrls ? (
                <>
                  {/* Before waveform container */}
                  <div
                    ref={beforeWaveformRef}
                    className={cn(
                      "w-full rounded-lg overflow-hidden bg-black/30",
                      !waveformLoaded && "animate-pulse h-[60px]",
                      activeVersion !== "before" && "hidden"
                    )}
                  />
                  {/* After waveform container */}
                  <div
                    ref={afterWaveformRef}
                    className={cn(
                      "w-full rounded-lg overflow-hidden bg-black/30",
                      !waveformLoaded && "animate-pulse h-[60px]",
                      activeVersion !== "after" && "hidden"
                    )}
                  />
                  {!waveformLoaded && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-sm text-gray-500">
                        Loading waveform...
                      </span>
                    </div>
                  )}
                </>
              ) : (
                <div className="w-full h-[60px] rounded-lg bg-black/30 flex items-center justify-center">
                  <span className="text-sm text-gray-500">
                    Waveform preview available after uploading audio files
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="space-y-4">
          {/* Playback Controls */}
          <div className="flex items-center gap-4">
            <Button
              onClick={togglePlayPause}
              disabled={!isLoaded || !hasValidAudioUrls}
              className="bg-orange-500 hover:bg-orange-600"
            >
              {isPlaying ? (
                <Pause className="w-4 h-4" />
              ) : (
                <Play className="w-4 h-4" />
              )}
            </Button>

            <Button
              onClick={resetPlayback}
              variant="outline"
              size="sm"
              disabled={!isLoaded || !hasValidAudioUrls}
            >
              <RotateCcw className="w-4 h-4" />
            </Button>

            <div className="flex-1 text-center">
              <div className="text-sm text-gray-400">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button onClick={toggleMute} variant="ghost" size="sm">
                {isMuted ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </Button>
              <Slider
                value={[isMuted ? 0 : volume]}
                onValueChange={handleVolumeChange}
                max={1}
                step={0.1}
                className="w-20"
              />
            </div>
          </div>

          {/* Progress Bar (fallback when no waveform) */}
          {(!showWaveform || !waveformLoaded) && (
            <div className="space-y-2">
              <Slider
                value={[currentTime]}
                onValueChange={handleSeek}
                max={duration}
                step={0.1}
                className="w-full"
                disabled={!isLoaded || !hasValidAudioUrls}
              />
            </div>
          )}
        </div>

        {/* Tags */}
        {comparison.tags && comparison.tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {comparison.tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

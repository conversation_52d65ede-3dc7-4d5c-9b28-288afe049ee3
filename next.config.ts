import type { NextConfig } from "next";
import remarkGfm from 'remark-gfm';
import remarkMdxImages from 'remark-mdx-images';
import rehypePrettyCode from 'rehype-pretty-code';

interface VisitNode {
  children: Array<{ type: string; value: string }>;
  properties: {
    className: string[];
  };
}

const nextConfig: NextConfig = {
  pageExtensions: ['ts', 'tsx', 'mdx'],
  
  images: {
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Configure webpack to handle MDX
  webpack: (config) => {
    config.module.rules.push({
      test: /\.mdx?$/,
      use: [
        {
          loader: '@mdx-js/loader',
          options: {
            remarkPlugins: [
              remarkGfm,
              remarkMdxImages,
            ],
            rehypePlugins: [
              [
                rehypePrettyCode,
                {
                  theme: 'github-dark',
                  keepBackground: true,
                  onVisitLine(node: VisitNode) {
                    if (node.children.length === 0) {
                      node.children = [{ type: 'text', value: ' ' }];
                    }
                  },
                  onVisitHighlightedLine(node: VisitNode) {
                    node.properties.className.push('highlighted');
                  },
                  onVisitHighlightedWord(node: VisitNode) {
                    node.properties.className = ['word'];
                  },
                },
              ],
            ],
          },
        },
      ],
    });
    return config;
  },
};

export default nextConfig;

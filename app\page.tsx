"use client";

import Image from "next/image";
import { motion, useScroll, useTransform } from "framer-motion";
import { TypeWriter } from "@/components/TypeWriter";
import { useAnimations } from "@/hooks/use-animations";
import Link from "next/link";

export default function Home() {
  const { scrollY } = useScroll();
  const { hasReducedMotion } = useAnimations();

  // Logo scroll animations
  const logoScale = useTransform(scrollY, [0, 300], [1, 0.8]);
  const logoY = useTransform(scrollY, [0, 300], [0, 50]);
  const logoOpacity = useTransform(scrollY, [0, 300], [1, 0.6]);

  const initialLogoAnimation = {
    scale: hasReducedMotion ? 1 : 0.8,
    opacity: 0,
  };

  const animateLogoTo = {
    scale: 1,
    opacity: 1,
    transition: {
      duration: hasReducedMotion ? 0 : 0.6,
      ease: "easeOut",
    },
  };

  return (
    <div className="relative min-h-screen overflow-hidden">
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8">
        {/* Logo with animations */}
        <motion.div
          className="w-full max-w-md mb-12"
          initial={initialLogoAnimation}
          animate={animateLogoTo}
          style={
            hasReducedMotion
              ? {}
              : {
                  scale: logoScale,
                  y: logoY,
                  opacity: logoOpacity,
                }
          }
        >
          <Image
            src="/NC Orange.png"
            alt="Noize Capital"
            width={300}
            height={100}
            priority
            className="w-full h-auto"
          />
        </motion.div>

        {/* Tagline with typewriter effect */}
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white text-center mb-8">
          <span className="sr-only">Noize Capital</span>
          <TypeWriter
            text="Precision Sound."
            className="block opacity-80 [text-shadow:_-1px_-1px_0_#000,_1px_-1px_0_#000,_-1px_1px_0_#000,_1px_1px_0_#000]"
            delay={hasReducedMotion ? 0 : 0.8}
          />
          <TypeWriter
            text="Independent Spirit."
            className="block text-orange-500 [text-shadow:_-1px_-1px_0_#000,_1px_-1px_0_#000,_-1px_1px_0_#000,_1px_1px_0_#000]"
            delay={hasReducedMotion ? 0 : 2}
          />
        </h1>

        {/* Content with fade-in */}
        <motion.div
          className="space-y-6 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{
            opacity: 1,
            y: 0,
            transition: {
              delay: hasReducedMotion ? 0 : 3,
              duration: hasReducedMotion ? 0 : 0.6,
            },
          }}
        >
          <p className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto">
            Where we make your ideas audible
          </p>

          <motion.div
            whileHover={hasReducedMotion ? {} : { scale: 1.05 }}
            whileTap={hasReducedMotion ? {} : { scale: 0.95 }}
          >
            <Link
              href="/services"
              className="inline-block bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-md font-medium transition-colors mt-4"
            >
              Our Services
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}

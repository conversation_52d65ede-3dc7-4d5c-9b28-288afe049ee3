"use client";

import { motion } from "framer-motion";
import { useAnimations } from "@/hooks/use-animations";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function AboutClient() {
  const {
    fadeInUp,
    fadeInLeft,
    fadeInRight,
    staggerChildren,
    hasReducedMotion,
  } = useAnimations();

  // Team members data
  const team = [
    {
      name: "<PERSON>",
      role: "Founder & Lead Engineer",
      bio: "With over 15 years of experience in beat making, <PERSON> has worked with upcoming artists and indie musicians alike.",
      image: "/melvin.jpg",
    },
  ];

  return (
    <div className="container mx-auto px-4 py-16 sm:py-24">
      <motion.div
        className="text-center mb-16"
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
      >
        <h1 className="text-4xl sm:text-5xl font-bold mb-4">About Us</h1>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          Passionate about sound, dedicated to your music
        </p>
      </motion.div>

      {/* Tabs for different sections */}
      <Tabs defaultValue="story" className="max-w-4xl mx-auto mb-20">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="story">Our Story</TabsTrigger>
          <TabsTrigger value="mission">Our Mission</TabsTrigger>
          <TabsTrigger value="studio">The Studio</TabsTrigger>
        </TabsList>

        <TabsContent value="story" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-4 text-gray-300"
          >
            <p>
              Noize Capital was founded in 2020 by Melvin Mpolokeng, an audio
              engineer with a passion for independent music. What started as a
              small project studio in Melvin&apos;s bedroom has grown into a
              full-service audio production company.
            </p>
            <p>
              Over the years, we&apos;ve had the privilege of working with
              artists across genres, from Hip-Hop to R&B and Afro-Tech. Our
              approach has always been the same: Treat every project with the
              same care and attention, whether it&apos;s a major label release
              or a debut EP.
            </p>
            <p>
              We&apos;ve expanded our services while maintaining our
              independent spirit and commitment to sonic excellence.
            </p>
          </motion.div>
        </TabsContent>

        <TabsContent value="mission" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-4 text-gray-300"
          >
            <p>
              Our mission is simple: to help independent artists achieve
              professional sound quality without compromising their creative
              vision. We believe that great music deserves great sound,
              regardless of budget or genre.
            </p>
            <p>
              We&apos;re committed to providing transparent, honest feedback
              and guidance throughout the production process. Our goal is not
              just to deliver a finished product, but to help artists grow and
              develop their sonic identity.
            </p>
            <p>
              At Noize Capital, we measure our success by the success of the
              artists we work with. When your music connects with listeners
              and achieves your creative goals, that&apos;s when we know
              we&apos;ve done our job right.
            </p>
          </motion.div>
        </TabsContent>

        <TabsContent value="studio" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-4 text-gray-300"
          >
            <p>
              Our studio is designed to provide both technical excellence and
              creative inspiration. We&apos;ve carefully selected our
              equipment and acoustic treatment to ensure accurate monitoring
              and optimal recording conditions.
            </p>
            <p>
              Beyond the technical specifications, we&apos;ve created a
              comfortable, inspiring environment where artists can feel at
              ease and focus on their performance. From the lighting to the
              decor, every element is designed to enhance the creative
              process.
            </p>
          </motion.div>
        </TabsContent>
      </Tabs>

      {/* Team section */}
      <motion.div
        className="mb-20"
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
      >
        <h2 className="text-3xl font-bold mb-12 text-center">
          Meet Our Team
        </h2>

        <motion.div
          className="grid grid-cols-1 place-items-center gap-6"
          variants={staggerChildren}
        >
          {team.map((member, index) => (
            <motion.div
              key={member.name}
              variants={index % 2 === 0 ? fadeInLeft : fadeInRight}
              className="h-full w-full max-w-sm"
            >
              <Card className="h-full border border-white/10 bg-black/50 backdrop-blur-sm overflow-hidden">
                <div className="relative h-48 w-full overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent z-10" />
                  <Image
                    src={member.image}
                    alt={`${member.name}`}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    priority
                    className="object-cover"
                  />
                </div>
                <CardHeader>
                  <CardTitle>{member.name}</CardTitle>
                  <CardDescription>{member.role}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-300">{member.bio}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>

      {/* Call to action */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0 }}
        animate={{
          opacity: 1,
          transition: { delay: hasReducedMotion ? 0 : 0.5 },
        }}
      >
        <h2 className="text-2xl font-bold mb-4">Want to work with us?</h2>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          We&apos;re always excited to collaborate with new artists and bring
          their vision to life.
        </p>
        <Button size="lg" className="bg-orange-500 hover:bg-orange-600">
          <Link href="/contact">Get in Touch</Link>
        </Button>
      </motion.div>
    </div>
  );
}

#!/usr/bin/env node

/**
 * Enhanced Test Runner with Automatic Retry
 * 
 * This script runs the test suite and automatically retries failed tests.
 * It provides better feedback and handles the entire test process.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

function log(message, type = 'info') {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  const prefix = {
    info: '📋',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    retry: '🔄',
    start: '🚀'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    log(`Created directory: ${dir}`);
  }
}

function runCommand(command, options = {}) {
  try {
    log(`Running: ${command}`, 'start');
    const result = execSync(command, { 
      stdio: 'inherit',
      encoding: 'utf8',
      ...options
    });
    return { success: true, result };
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      code: error.status || 1
    };
  }
}

async function runTestsWithRetry() {
  log('🧪 Starting Enhanced Test Suite with Auto-Retry', 'start');
  
  // Ensure test-results directory exists
  ensureDirectoryExists('test-results');
  
  // Step 1: Run initial test suite
  log('\n📝 Step 1: Running initial test suite...');
  const initialResult = runCommand('npx playwright test essential.spec.ts --reporter=json,html,list');
  
  if (initialResult.success) {
    log('🎉 All tests passed on first run!', 'success');
    log('📊 Opening test report...');
    
    // Try to open the report (optional)
    try {
      runCommand('npx playwright show-report', { stdio: 'ignore' });
    } catch (e) {
      // Ignore if can't open report
    }
    
    return { success: true, totalTests: 0, failedTests: 0, retriedTests: 0 };
  }
  
  log('⚠️ Some tests failed. Proceeding to retry phase...', 'warning');
  
  // Step 2: Run retry script
  log('\n🔄 Step 2: Running automatic retry for failed tests...');
  const retryResult = runCommand('node scripts/retry-failed-tests.js');
  
  // Step 3: Generate final report
  log('\n📊 Step 3: Generating final test report...');
  
  // Count results from JSON file if it exists
  let testStats = { totalTests: 0, failedTests: 0, retriedTests: 0 };
  
  try {
    if (fs.existsSync('test-results/results.json')) {
      const results = JSON.parse(fs.readFileSync('test-results/results.json', 'utf8'));
      
      if (results.suites) {
        results.suites.forEach(suite => {
          if (suite.specs) {
            suite.specs.forEach(spec => {
              if (spec.tests) {
                testStats.totalTests += spec.tests.length;
                spec.tests.forEach(test => {
                  if (test.results) {
                    const hasFailure = test.results.some(r => 
                      r.status === 'failed' || r.status === 'timedOut'
                    );
                    if (hasFailure) {
                      testStats.failedTests++;
                    }
                  }
                });
              }
            });
          }
        });
      }
    }
  } catch (error) {
    log(`Warning: Could not parse test results: ${error.message}`, 'warning');
  }
  
  // Final summary
  log('\n📈 Final Test Summary:');
  log(`   Total tests: ${testStats.totalTests || 'Unknown'}`);
  log(`   Failed tests: ${testStats.failedTests || 'Unknown'}`);
  log(`   Success rate: ${testStats.totalTests > 0 ? 
    Math.round(((testStats.totalTests - testStats.failedTests) / testStats.totalTests) * 100) : 'Unknown'}%`);
  
  if (retryResult.success) {
    log('\n🎉 Test suite completed successfully after retries!', 'success');
    return { success: true, ...testStats };
  } else {
    log('\n❌ Some tests are still failing after retries.', 'error');
    log('💡 Consider reviewing the failing tests manually.', 'warning');
    return { success: false, ...testStats };
  }
}

function printUsage() {
  console.log(`
🧪 Enhanced Test Runner Usage:

Commands:
  npm run test:full     - Run tests with automatic retry
  npm run test         - Run tests once (no retry)
  npm run test:retry   - Retry only failed tests from last run
  npm run test:ui      - Run tests in interactive UI mode
  npm run test:headed  - Run tests with visible browser
  npm run test:debug   - Run tests in debug mode

Examples:
  npm run test:full    # Recommended for CI/CD
  npm run test:ui      # Best for development
  npm run test:retry   # After fixing issues
`);
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    printUsage();
    return;
  }
  
  try {
    const result = await runTestsWithRetry();
    
    // Open report if tests completed
    if (result.success) {
      log('\n📊 Opening test report in browser...');
      setTimeout(() => {
        try {
          runCommand('npx playwright show-report', { stdio: 'ignore' });
        } catch (e) {
          log('Could not open report automatically. Run "npm run test:report" to view it.', 'warning');
        }
      }, 1000);
    }
    
    process.exit(result.success ? 0 : 1);
  } catch (error) {
    log(`Unexpected error: ${error.message}`, 'error');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { runTestsWithRetry };

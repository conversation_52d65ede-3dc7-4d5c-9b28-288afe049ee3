import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, List, Music, TrendingUp } from "lucide-react";

export const metadata: Metadata = {
  title: "Admin Dashboard | Noize Capital",
  description: "Admin dashboard for managing Noize Capital content",
  robots: "noindex, nofollow",
};

export default function AdminDashboard() {
  return (
    <div className="container mx-auto px-4 py-16 sm:py-24">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Admin Dashboard</h1>
          <p className="text-xl text-gray-300">
            Manage your audio portfolio and content
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <Card className="border-white/10 bg-black/50 backdrop-blur-sm hover:bg-black/60 transition-colors">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="w-5 h-5 text-orange-500" />
                Upload Audio Files
              </CardTitle>
              <CardDescription>
                Add new audio files for portfolio comparisons
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/admin/audio">
                <Button className="w-full bg-orange-500 hover:bg-orange-600">
                  Go to Upload
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="border-white/10 bg-black/50 backdrop-blur-sm hover:bg-black/60 transition-colors">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <List className="w-5 h-5 text-orange-500" />
                Manage Files
              </CardTitle>
              <CardDescription>
                View, search, and manage uploaded audio files
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/admin/manage">
                <Button className="w-full bg-orange-500 hover:bg-orange-600">
                  Manage Files
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-white/10 bg-black/50 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <Music className="w-8 h-8 mx-auto mb-4 text-orange-500" />
              <div className="text-2xl font-bold mb-2">Audio Portfolio</div>
              <div className="text-sm text-gray-400">
                Showcase your before/after comparisons
              </div>
            </CardContent>
          </Card>

          <Card className="border-white/10 bg-black/50 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <TrendingUp className="w-8 h-8 mx-auto mb-4 text-orange-500" />
              <div className="text-2xl font-bold mb-2">Vercel Blob</div>
              <div className="text-sm text-gray-400">
                Secure cloud storage for audio files
              </div>
            </CardContent>
          </Card>

          <Card className="border-white/10 bg-black/50 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <Upload className="w-8 h-8 mx-auto mb-4 text-orange-500" />
              <div className="text-2xl font-bold mb-2">Easy Upload</div>
              <div className="text-sm text-gray-400">
                Drag & drop interface with metadata
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <Card className="border-white/10 bg-black/50 backdrop-blur-sm mt-12">
          <CardHeader>
            <CardTitle>Getting Started</CardTitle>
            <CardDescription>
              How to use the audio portfolio system
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-orange-500 text-white text-sm flex items-center justify-center font-bold mt-0.5">
                1
              </div>
              <div>
                <h4 className="font-medium mb-1">Upload Audio Files</h4>
                <p className="text-sm text-gray-400">
                  Use the upload interface to add your mixed/unmixed and mastered/unmastered audio files.
                  Fill in the metadata for proper categorization.
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-orange-500 text-white text-sm flex items-center justify-center font-bold mt-0.5">
                2
              </div>
              <div>
                <h4 className="font-medium mb-1">Organize by Projects</h4>
                <p className="text-sm text-gray-400">
                  Group related files using the same Project ID. This allows for proper before/after comparisons.
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-orange-500 text-white text-sm flex items-center justify-center font-bold mt-0.5">
                3
              </div>
              <div>
                <h4 className="font-medium mb-1">View on Portfolio</h4>
                <p className="text-sm text-gray-400">
                  Your uploaded files will automatically appear in the portfolio page with interactive comparison players.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

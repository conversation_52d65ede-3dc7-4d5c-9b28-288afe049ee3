"use client";

import { motion } from "framer-motion";
import { useAnimations } from "@/hooks/use-animations";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useEffect, useState } from "react";
import { getExchangeRates, convertCurrency } from "@/lib/exchange-rates";

// Currency configuration
const CURRENCIES = {
  USD: {
    symbol: "$",
    locale: "en-US",
  },
  ZAR: {
    symbol: "R",
    locale: "en-ZA",
  },
};

export default function ServicesClient() {
  const { hasReducedMotion, fadeInUp, staggerChildren, cardHover } =
    useAnimations();
  const [currency, setCurrency] = useState("ZAR");
  const [exchangeRates, setExchangeRates] = useState<Record<string, number>>({});
  const [isLoadingRates, setIsLoadingRates] = useState(true);

  // Initialize currency based on user's locale
  useEffect(() => {
    const savedCurrency = localStorage.getItem("preferredCurrency");
    if (savedCurrency) {
      setCurrency(savedCurrency);
    } else {
      const userLocale = navigator.language || navigator.languages[0];
      const isSouthAfrican = userLocale.toLowerCase().includes("za") || 
                           userLocale.toLowerCase().includes("af") ||
                           userLocale.toLowerCase().includes("en-za");
      setCurrency(isSouthAfrican ? "ZAR" : "USD");
    }
  }, []);

  // Fetch exchange rates
  useEffect(() => {
    async function fetchRates() {
      try {
        const rates = await getExchangeRates();
        setExchangeRates(rates);
      } catch (error) {
        console.error('Error fetching exchange rates:', error);
      } finally {
        setIsLoadingRates(false);
      }
    }

    fetchRates();
    // Refresh rates every hour
    const interval = setInterval(fetchRates, 1000 * 60 * 60);
    return () => clearInterval(interval);
  }, []);

  // Save currency preference
  const handleCurrencyChange = (newCurrency: string) => {
    setCurrency(newCurrency);
    localStorage.setItem("preferredCurrency", newCurrency);
  };

  const formatPrice = (usdPrice: number) => {
    const currencyConfig = CURRENCIES[currency as keyof typeof CURRENCIES];
    let convertedPrice = usdPrice;

    if (currency !== "USD" && Object.keys(exchangeRates).length > 0) {
      convertedPrice = convertCurrency(usdPrice, "USD", currency, exchangeRates);
    }

    // Use a consistent format for both server and client
    return new Intl.NumberFormat(currencyConfig.locale, {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      useGrouping: true,
    }).format(convertedPrice);
  };

  // Add a client-side only effect to handle initial price formatting
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  const services = [
    {
      title: "Mixing",
      emoji: "🎛️",
      description:
        "Professional mixing to make your tracks sound balanced and polished.",
      features: [
        "Up to 8 tracks",
        "2 revisions",
        "Basic effects and processing",
        "Delivery in 7 days",
      ],
      usdPrice: 35,
      popular: false,
    },
    {
      title: "Mastering",
      emoji: "🎚️",
      description:
        "Take your mixed tracks to the next level with professional mastering.",
      features: [
        "Stereo track mastering",
        "3 revisions",
        "Advanced processing",
        "Delivery in 5 days",
        "Streaming-ready formats",
      ],
      usdPrice: 20,
      popular: true,
    },
    {
      title: "Full Package",
      emoji: "💽",
      description:
        "Complete audio production from mixing to mastering for the best results.",
      features: [
        "Up to 16 tracks",
        "5 revisions",
        "Premium effects and processing",
        "Delivery in 10 days",
        "All digital formats",
        "Stem mastering",
      ],
      usdPrice: 50,
      popular: false,
    },
  ];

  const faqs = [
    {
      question: "What formats do you accept?",
      answer:
        "We accept WAV, AIFF, and high-quality MP3 files. For best results, we recommend sending uncompressed WAV or AIFF files at 44.1kHz/24-bit or higher.",
    },
    {
      question: "How many revisions are included?",
      answer:
        "The number of revisions depends on the package you choose. Mixing includes 2 revisions, Mastering includes 3 revisions, and the Full Package includes 5 revisions. Additional revisions can be purchased if needed.",
    },
    {
      question: "What's your turnaround time?",
      answer:
        "Turnaround times vary by service: Mixing is delivered in 7 days, Mastering in 5 days, and the Full Package in 10 days. Rush services are available for an additional fee.",
    },
    {
      question: "Do you offer discounts for multiple tracks?",
      answer:
        "Yes, we offer discounts for projects with multiple tracks. Contact us for a custom quote based on your specific needs.",
    },
    {
      question: "What payment methods do you accept?",
      answer:
        "We accept PayPal and bank transfers. A 50% deposit is required to start work, with the remaining balance due before final delivery.",
    },
  ];

  return (
    <div className="container mx-auto px-4 py-16 sm:py-24">
      <motion.div
        className="text-center mb-16"
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
      >
        <h1 className="text-4xl sm:text-5xl font-bold mb-4">Our Services</h1>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          Professional audio services to make your music sound its best
        </p>
        <div className="mt-4 flex justify-center items-center gap-2">
          <span className="text-gray-300">Currency:</span>
          <select
            value={currency}
            onChange={(e) => handleCurrencyChange(e.target.value)}
            className="bg-black/30 border border-white/20 rounded-md p-2 text-white"
            disabled={isLoadingRates}
          >
            <option value="USD">USD ($)</option>
            <option value="ZAR">ZAR (R)</option>
          </select>
          {isLoadingRates && (
            <span className="text-sm text-gray-400">Loading rates...</span>
          )}
        </div>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 mb-20"
        initial="hidden"
        animate="visible"
        variants={staggerChildren}
      >
        {services.map((service) => (
          <motion.div
            key={service.title}
            variants={fadeInUp}
            whileHover={cardHover}
            className="h-full"
          >
            <Card
              className={`h-full border border-white/10 bg-black/50 backdrop-blur-sm overflow-hidden ${
                service.popular ? "ring-2 ring-orange-500" : ""
              }`}
            >
              {service.popular && (
                <div className="absolute top-0 right-0 bg-orange-500 text-white px-3 py-1 text-sm font-medium rounded-bl-md rounded-tr-md">
                  Popular
                </div>
              )}
              <CardHeader>
                <div className="text-4xl mb-2">{service.emoji}</div>
                <CardTitle className="text-2xl">{service.title}</CardTitle>
                <CardDescription>{service.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold mb-4">
                  {mounted ? formatPrice(service.usdPrice) : `${CURRENCIES[currency as keyof typeof CURRENCIES].symbol}${service.usdPrice.toFixed(2)}`}
                </div>
                <ul className="space-y-2 text-sm md:text-base">
                  {service.features.map((feature) => (
                    <li key={feature} className="flex items-start">
                      <span className="text-orange-500 mr-2 mt-0.5 flex-shrink-0">
                        ✓
                      </span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full bg-orange-500 hover:bg-orange-600">
                  <Link href="/contact" className="w-full">
                    Get Started
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      <motion.div
        className="max-w-3xl mx-auto"
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
      >
        <h2 className="text-3xl font-bold mb-8 text-center">
          Frequently Asked Questions
        </h2>
        <Accordion
          type="single"
          collapsible
          className="bg-black/30 backdrop-blur-sm rounded-lg p-2 sm:p-4 border border-white/10"
        >
          {faqs.map((faq, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-sm sm:text-lg font-medium">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-gray-300 text-sm sm:text-base">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </motion.div>

      <motion.div
        className="text-center mt-16"
        initial={{ opacity: 0 }}
        animate={{
          opacity: 1,
          transition: { delay: hasReducedMotion ? 0 : 0.5 },
        }}
      >
        <h2 className="text-2xl font-bold mb-4">
          Ready to elevate your sound?
        </h2>
        <p className="text-gray-300 mb-6">
          Contact us today to discuss your project and get started.
        </p>
        <Button size="lg" className="bg-orange-500 hover:bg-orange-600">
          <Link href="/contact">Contact Us</Link>
        </Button>
      </motion.div>
    </div>
  );
}

"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { useEffect, useState } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { Menu, ChevronRight } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";

export function Navigation() {
  const pathname = usePathname();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const isMobile = useIsMobile();
  const [hasReducedMotion, setHasReducedMotion] = useState(false);

  // Check for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    setHasReducedMotion(mediaQuery.matches);

    const onChange = () => setHasReducedMotion(mediaQuery.matches);
    mediaQuery.addEventListener("change", onChange);
    return () => mediaQuery.removeEventListener("change", onChange);
  }, []);

  const navItems = [
    { name: "Home", href: "/" },
    { name: "Services", href: "/services" },
    { name: "Portfolio", href: "/portfolio" },
    { name: "Blog", href: "/blog" },
    { name: "About", href: "/about" },
    { name: "Contact", href: "/contact" },
  ];

  // Mobile navigation with enhanced animations
  const MobileNav = () => (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden relative overflow-hidden group"
        >
          <Menu className="h-6 w-6 text-white transition-transform duration-200 group-hover:scale-110" />
          <span className="sr-only">Toggle menu</span>
          <div className="absolute inset-0 rounded-md ring-2 ring-orange-500/20 group-hover:ring-orange-500/40 transition-all duration-300" />
        </Button>
      </SheetTrigger>
      <SheetContent
        side="right"
        className="bg-gradient-to-b from-black/95 to-black/90 backdrop-blur-md border-white/10 p-0 overflow-hidden"
      >
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: hasReducedMotion ? 0 : 0.2 }}
          className="h-full"
        >
          <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
          <nav className="flex flex-col h-full pt-12">
            <div className="flex-1 px-6">
              {navItems.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ x: 50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{
                    duration: hasReducedMotion ? 0 : 0.3,
                    delay: hasReducedMotion ? 0 : index * 0.1,
                  }}
                >
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center justify-between text-lg font-medium py-4 border-b border-white/10",
                      "transition-all duration-200 hover:pl-2",
                      pathname === item.href
                        ? "text-orange-500 border-orange-500/50"
                        : "text-white hover:text-orange-500 hover:border-orange-500/30"
                    )}
                  >
                    <span>{item.name}</span>
                    <ChevronRight
                      className={cn(
                        "h-4 w-4 transition-transform",
                        pathname === item.href
                          ? "text-orange-500"
                          : "text-white/50",
                        "group-hover:translate-x-1"
                      )}
                    />
                  </Link>
                </motion.div>
              ))}
            </div>
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{
                duration: hasReducedMotion ? 0 : 0.3,
                delay: hasReducedMotion ? 0 : navItems.length * 0.1,
              }}
              className="p-6 pt-0"
            >
              <Link
                href="/contact"
                className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium rounded-lg px-4 py-2 flex items-center justify-center gap-2 transition-colors"
              >
                Book Now
                <ChevronRight className="h-4 w-4" />
              </Link>
            </motion.div>
          </nav>
        </motion.div>
      </SheetContent>
    </Sheet>
  );

  return (
    <motion.div
      className="fixed top-0 left-0 right-0 z-50 bg-black/30 backdrop-blur-md border-b border-white/10"
      initial={{ y: -100, opacity: 0 }}
      animate={{
        y: 0,
        opacity: 1,
        transition: {
          duration: hasReducedMotion ? 0 : 0.5,
          ease: "easeOut",
        },
      }}
    >
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        <Link href="/" className="text-white font-bold text-xl">
          Noize Capital
        </Link>

        {/* Desktop Navigation */}
        <NavigationMenu className="hidden md:flex">
          <NavigationMenuList>
            {navItems.map((item) => (
              <NavigationMenuItem key={item.name}>
                <NavigationMenuLink
                  asChild
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "text-white hover:text-orange-500 transition-colors",
                    pathname === item.href && "text-orange-500"
                  )}
                >
                  <Link href={item.href}>{item.name}</Link>
                </NavigationMenuLink>
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>

        {/* Mobile Navigation */}
        <MobileNav />

        {/* Book Now Button (Desktop) */}
        <motion.div
          className="hidden md:block"
          whileHover={hasReducedMotion ? {} : { scale: 1.05 }}
          whileTap={hasReducedMotion ? {} : { scale: 0.95 }}
        >
          <Link
            href="/contact"
            className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md font-medium transition-colors"
          >
            Book Now
          </Link>
        </motion.div>
      </div>
    </motion.div>
  );
}
